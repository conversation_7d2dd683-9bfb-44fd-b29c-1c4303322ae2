import '@ionic/vue';
import {
  AllowedComponentProps,
  ComponentCustomProps,
  ComponentOptionsMixin,
  ComputedOptions,
  DefineComponent,
  MethodOptions,
  VNodeProps,
} from 'vue';
import { InputProps } from '@ionic/vue/dist/types/vue-component-lib/utils';
import { JSX } from '@ionic/core/components';

declare module '@ionic/vue' {
  export type IonSelectVModelType = any | undefined;
  export type IonSelectMultipleVModelType = IonSelectVModelType[];
  export const IonSelect: DefineComponent<
    JSX.IonSelect & InputProps<IonSelectVModelType | IonSelectMultipleVModelType>,
    object,
    // eslint-disable-next-line @typescript-eslint/ban-types
    {},
    ComputedOptions,
    MethodOptions,
    ComponentOptionsMixin,
    ComponentOptionsMixin,
    // eslint-disable-next-line @typescript-eslint/ban-types
    {},
    string,
    VNodeProps & AllowedComponentProps & ComponentCustomProps,
    Readonly<JSX.IonSelect & InputProps<IonSelectVModelType | IonSelectMultipleVModelType>>,
    {
      value?: any;
      interfaceOptions?: any;
    }
  >;
}
