(function(n,o){typeof exports=="object"&&typeof module<"u"?module.exports=o(require("vue"),require("@ionic/vue"),require("@incomnetworking/app-agent-view-leads-page")):typeof define=="function"&&define.amd?define(["vue","@ionic/vue","@incomnetworking/app-agent-view-leads-page"],o):(n=typeof globalThis<"u"?globalThis:n||self,n.AppAgentDoubleViewLeadsPage=o(n.Vue,n.vue$1,n.LeadsPage))})(this,function(n,o,c){"use strict";const s=(e=>e&&typeof e=="object"&&"default"in e?e:{default:e})(c),l=n.defineComponent({components:{actionSheetController:o.actionSheetController,IonRouterOutlet:o.IonRouterOutlet,IonMenu:o.Ion<PERSON>u,IonButton:o.<PERSON>,IonContent:<PERSON><PERSON>,IonFooter:<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>:<PERSON><PERSON>,IonIcon:<PERSON>.<PERSON>,IonList:<PERSON><PERSON>,IonPage:o.IonPage,IonSpinner:o.IonSpinner,IonToolbar:o.IonToolbar,menuController:o.menuController,IonLabel:o.IonLabel,IonSkeletonText:o.IonSkeletonText,IonInfiniteScroll:o.IonInfiniteScroll,IonInfiniteScrollContent:o.IonInfiniteScrollContent,IonItem:o.IonItem,IonSplitPane:o.IonSplitPane,IonRefresher:o.IonRefresher,IonRefresherContent:o.IonRefresherContent,LeadsPage:s.default},ionViewWillEnter(){},ionViewDidEnter(){},data(){return{isSplit:!1}},computed:{},methods:{},mounted:function(){},beforeUnmount:function(){}}),p=(e,i)=>{const t=e.__vccOpts||e;for(const[r,a]of i)t[r]=a;return t},d={class:"ion-page",id:"main"};function I(e,i,t,r,a,w){const f=n.resolveComponent("LeadsPage"),_=n.resolveComponent("ion-content"),m=n.resolveComponent("ion-menu"),C=n.resolveComponent("ion-router-outlet"),u=n.resolveComponent("ion-split-pane"),g=n.resolveComponent("ion-page");return n.openBlock(),n.createBlock(g,null,{default:n.withCtx(()=>[n.createVNode(u,{when:"md","content-id":"main"},{default:n.withCtx(()=>[n.createVNode(m,{"content-id":"main"},{default:n.withCtx(()=>[n.createVNode(_,{class:"ion-padding"},{default:n.withCtx(()=>[n.createVNode(f)]),_:1})]),_:1}),n.createElementVNode("div",d,[n.createVNode(C)])]),_:1})]),_:1})}return p(l,[["render",I]])});
