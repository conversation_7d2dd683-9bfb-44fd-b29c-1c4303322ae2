import { createApp } from "vue";
import Debug from "@gormartsen/vue-debug";

import App from "./App.vue";



/* Core CSS required for Ionic components to work properly */
import '@ionic/vue/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/vue/css/normalize.css';
import '@ionic/vue/css/structure.css';
import '@ionic/vue/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/vue/css/padding.css';
import '@ionic/vue/css/float-elements.css';
import '@ionic/vue/css/text-alignment.css';
import '@ionic/vue/css/text-transformation.css';
import '@ionic/vue/css/flex-utils.css';
import '@ionic/vue/css/display.css';

/* Theme variables */
import '@incomnetworking/app-agent-fonts-scss/fonts/sourcesanspro.scss';
import '@incomnetworking/app-agent-fonts-scss/fonts/quicksand.scss';
import '@incomnetworking/app-agent-fonts-scss/fonts/asap.scss';
import '@incomnetworking/app-agent-fonts-scss/fonts/mobile-font.scss';
import '@incomnetworking/app-agent-fonts-scss/variables.css';
import '@incomnetworking/app-agent-global-scss/scss/global.scss';


var APP = createApp(App);
APP.use(Debug, true);
// Short Code names for phrases
import InterfaceTextPlugin from "@incomnetworking/interface-text";
import InterfaceBundleTranslations from "@incomnetworking/interface-translations";
APP.use(InterfaceTextPlugin, InterfaceBundleTranslations);

// Persist state this.$state global variable
var sessionName = "API-Browser-APP";
import defaultSettings from "./settings/state";
import PersistState from "vue-persist-state";
APP.use(PersistState, sessionName, defaultSettings);

// Dataset this.$dataset global variable
import DatasetState from "@gormartsen/vue-dataset";
import defaultDataSet from "./settings/dataset";
APP.use(DatasetState, defaultDataSet);

// API this.$api global variable
import ApiClient from "@microservice-framework/vue-api-client";
import apiSettings from "@incomnetworking/pagebuild-api-settings-dashboard";
APP.use(ApiClient, apiSettings);

// API this.$helpers global variable
import APPHelpers from "@incomnetworking/vue-plugin-app-helper";
APP.use(APPHelpers);

// API this.$auth global variable
import APPAuth from "@incomnetworking/vue-plugin-auth";
APP.use(APPAuth);

import { createPinia, setMapStoreSuffix } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

import { IonicVue } from "@ionic/vue";

/* main theme */
import "@incomnetworking/app-agent-interface-styles";

APP.use(IonicVue, {
  mode: "ios",
  scrollAssist: true,
  autoFocusAssist: false,
  swipeBackEnabled: true,
});

setMapStoreSuffix("Store");
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

APP.use(pinia);

import router from "./router";

import { useNewLeadStore } from '@incomnetworking/app-agent-lead-store';
await useNewLeadStore().initializeState();

router
  .isReady()
  .then(() => {
    console.log("Router initialized successfully");
    APP.mount("#app");
  })
  .catch((error) => {
    console.error("Router failed to initialize:", error);
  });

  
APP.use(router);


// Make APP accessable in browser console
window.APP = APP;
