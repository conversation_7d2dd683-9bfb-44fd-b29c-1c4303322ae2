<style lang="scss" scoped></style>
<template>
  <ion-app>
    <main-menu />
    <ion-router-outlet
      id="main-content"
      class="router-outlet-animated-call-margin"
    />
  </ion-app>
</template>
<script>
import MicroserviceClient from "@microservice-framework/microservice-client";
import MainMenu from "@incomnetworking/app-agent-main-menu-vue-component";
import { IonApp, IonRouterOutlet } from "@ionic/vue";

export default {
  // Properties returned from data() become reactive state
  // and will be exposed on `this`.
  components: { IonApp, IonRouterOutlet, MainMenu },
  data: function () {
    return {
      TextTest: "sometext",
    };
  },
  watch: {},
  mounted: function () {
    // Use access tocken from local storage
    var AccessToken = false;
    if (window.accessToken) {
      AccessToken = window.accessToken;
    }
    if (this.$state.accessToken !== "") {
      AccessToken = this.$state.accessToken;
    }
    if (AccessToken !== false) {
      var client = new MicroserviceClient({
        URL: this.$api.url,
        accessToken: AccessToken,
        headers: { scope: "auth" },
      });
      client.get("auth/" + AccessToken).then((response) => {
        this.$debug.info("accessToken", response);
        if (response.error) {
          this.authError = response.error.message;
          return;
        }
        this.$auth.logIn(response.answer);
      });
    }
  },
};
</script>
