# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@anuradev/capacitor-audio-toggle@2.0.6":
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/@anuradev/capacitor-audio-toggle/-/capacitor-audio-toggle-2.0.6.tgz#c386fb3dbab45d9cb95fd6d2da99f1b38b3b854b"
  integrity sha512-49i+8xaIVLE2SSLNmohw4XL9yKMSy1/xyxDJ7UwRwyhLF9IQZuLY6cpc5bttyUMB9vAV6QbmrSWyNk0xpMRDKA==

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry.yarnpkg.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://registry.yarnpkg.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==

"@babel/parser@^7.28.0":
  version "7.28.0"
  resolved "https://registry.yarnpkg.com/@babel/parser/-/parser-7.28.0.tgz#979829fbab51a29e13901e5a80713dbcb840825e"
  integrity sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==
  dependencies:
    "@babel/types" "^7.28.0"

"@babel/types@^7.28.0":
  version "7.28.2"
  resolved "https://registry.yarnpkg.com/@babel/types/-/types-7.28.2.tgz#da9db0856a9a88e0a13b019881d7513588cf712b"
  integrity sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@byteowls/capacitor-sms@^6.0.0":
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/@byteowls/capacitor-sms/-/capacitor-sms-6.0.0.tgz#73e8c0a1a24d5e23a39334be134807712af4a0a4"
  integrity sha512-njuPeIoEY+mhyB1gOKaWAJb+2J/QHGeTT/dLqbYoY+xMFjYaVz6A0snen1kQBB+7+/z3KR0KmCHjq9e+Xk/0/Q==

"@capacitor/browser@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/browser/-/browser-7.0.1.tgz#efcf3ec0bdc9e580f49db26ae982509b354d478a"
  integrity sha512-N6KEVLw2enTnourQzYJLvAkSds2Ed21zqsvHnSImrVDenzX8fUj032kMt4EdewmxfxiEwRa911BT1VOPBi0fEA==

"@capacitor/camera@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/camera/-/camera-7.0.1.tgz#bcbd1c05cce648fa54b844bf7a6745854cae6ba4"
  integrity sha512-gDUFsYlhMra5VVOa4iJV6+MQRhp3VXpTLQY4JDATj7UvoZ8Hv4DG8qplPL9ufUFNoR3QbDDnf8+gbQOsKdkDjg==

"@capacitor/core@^7.2.0":
  version "7.4.2"
  resolved "https://registry.yarnpkg.com/@capacitor/core/-/core-7.4.2.tgz#9ed8f4fe80a24c51c6969dbf44b70142d1e8eb91"
  integrity sha512-akCf9A1FUR8AWTtmgGjHEq6LmGsjA2U7igaJ9PxiCBfyxKqlDbuGHrlNdpvHEjV5tUPH3KYtkze6gtFcNKPU9A==
  dependencies:
    tslib "^2.1.0"

"@capacitor/device@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/device/-/device-7.0.1.tgz#d7aa792c1025c45855998691b4b4c4f4b3acbceb"
  integrity sha512-ImjR2LmCEhbckYtOHXTfi5v6B7d4HPX9uKIAQs7wzzmE+yRRXuUTEzARBQ00dZDInAInkbuyQRdjwPTSHZUpnw==

"@capacitor/dialog@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/dialog/-/dialog-7.0.1.tgz#aa591ef8a5d86110e2f39de7f84833da2b48bff4"
  integrity sha512-0MrGknjGg98LOKZN1SpV4/QfL3G7qE4j/Gs5+P+fi63UKTmwN+HqsZcavU/1ghThEDG7Emiv/uEdZeHaqbaUIg==

"@capacitor/geolocation@^7.1.2":
  version "7.1.4"
  resolved "https://registry.yarnpkg.com/@capacitor/geolocation/-/geolocation-7.1.4.tgz#0414815c250467377faa4e6f456144e681de54ff"
  integrity sha512-8KQfa1RNVmgFoDYYaf13qDuf5rh7ob4mC/zRpzpavPEFhWkuIbFM5dyNpSndc9EFnaIib8BCIjJj9ZQ6+nPrTQ==
  dependencies:
    "@capacitor/synapse" "^1.0.3"

"@capacitor/keyboard@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/keyboard/-/keyboard-7.0.1.tgz#edf212528cd1587494ceb1d260868c1a9540aed1"
  integrity sha512-Gi064vOARMac+x9/DmEFeywN9oAETMf3OYsMuYm9gA8SvdsDJ3QJqMoFnSEIORYXe21Jzt2SIEdLlpT65P/b2g==

"@capacitor/preferences@^5.0.5":
  version "5.0.8"
  resolved "https://registry.yarnpkg.com/@capacitor/preferences/-/preferences-5.0.8.tgz#46ba54432d7c5b248b7a91736c9718a6b736198f"
  integrity sha512-zzz8JC2NuZ+xdBP2Cfhu4uyRUMAFoxMl7l8w5ahQPzckyt7Fk/pWATXj6IcTm7DzbsKc8ryXSsYTkv9ZL3Pfmw==

"@capacitor/preferences@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/preferences/-/preferences-7.0.1.tgz#d1791a94fba8018497839fb7bd0bfa8b636dad6d"
  integrity sha512-XF9jOHzvoIBZLwZr/EX6aVaUO1d8Mx7TwBLQS33pYHOliCW5knT5KUkFOXNNYxh9qqODYesee9xuQIKNJpQBag==

"@capacitor/push-notifications@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/push-notifications/-/push-notifications-7.0.1.tgz#faa73aa2d0cc3c937d4331b9f010b123090a1185"
  integrity sha512-nSHsMSrTHX5pOkX1Khse75/uvSx/JTcXG+9aT6a66CvzalH6MCs0ha8Jv+xu0k9xW8caO+qSUMjfj5Oy82Uxmw==

"@capacitor/share@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/share/-/share-7.0.1.tgz#1d1e6d312504703c1f34fa0de94bed14af4bcbf8"
  integrity sha512-7GAtWrb2inEWohC8E7mx38qAX6D9yqPDDnUtJaZ8JRpo15jjFRS40Cx388M8h4NlBWjV5NU3qf1sHXnyOBSJ5g==

"@capacitor/synapse@^1.0.3":
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/@capacitor/synapse/-/synapse-1.0.4.tgz#c6beb33119d9656b1f04cb7783989fb78933ef6d"
  integrity sha512-/C1FUo8/OkKuAT4nCIu/34ny9siNHr9qtFezu4kxm6GY1wNFxrCFWjfYx5C1tUhVGz3fxBABegupkpjXvjCHrw==

"@capawesome-team/capacitor-file-opener@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capawesome-team/capacitor-file-opener/-/capacitor-file-opener-7.0.1.tgz#638c4c5c8783924f94b44335ca2a05ce4ab4854d"
  integrity sha512-Cf0z65pCLcSzgIaY8G2pp5iJSVbR88IrjfIM1DeCBpHxZV4V17SCA5uZ7eFhaReF7UahGbAAo2JBsmv5M5n0WQ==

"@capawesome/capacitor-badge@^7.0.1":
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/@capawesome/capacitor-badge/-/capacitor-badge-7.0.1.tgz#56fb13e50f14afc38da403b61a9bf7da4aa83b52"
  integrity sha512-jhVieRRVLgGO1NU7PW8uWZmf3WD4IsYUlkrJ82KuoRgLFx1tbJGwHU1ro0sUJmEwfLO9vldhBnJJ/J5nHrjbQQ==

"@capawesome/capacitor-file-picker@^7.1.0":
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/@capawesome/capacitor-file-picker/-/capacitor-file-picker-7.2.0.tgz#f5831df0e402ac33ddd9b7df19346aadab2026bf"
  integrity sha512-qUNa+Ypem575xSNiJwgiOaaHXzAHaxMtERySRIsIC0HggoCa11xGS3xEVtMooQ3fS0cDXfl4/17DYoHQtkDjXA==

"@esbuild/android-arm@0.15.18":
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.15.18.tgz#266d40b8fdcf87962df8af05b76219bc786b4f80"
  integrity sha512-5GT+kcs2WVGjVs7+boataCkO5Fg0y4kCjzkB5bAip7H4jfnOS3dA6KPiww9W1OEKTKeAcUVhdZGvgI65OXmUnw==

"@esbuild/linux-loong64@0.15.18":
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.15.18.tgz#128b76ecb9be48b60cf5cfc1c63a4f00691a3239"
  integrity sha512-L4jVKS82XVhw2nvzLg/19ClLWg0y27ulRwuP7lcyL6AbUWB5aPglXY3M21mauDQMDfRLs8cQmeT03r/+X3cZYQ==

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  version "4.7.0"
  resolved "https://registry.yarnpkg.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "https://registry.yarnpkg.com/@eslint-community/regexpp/-/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://registry.yarnpkg.com/@eslint/eslintrc/-/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "https://registry.yarnpkg.com/@eslint/js/-/js-8.57.1.tgz#de633db3ec2ef6a3c89e2f19038063e8a122e2c2"
  integrity sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==

"@fawazahmed0/currency-api@^2024.7.8":
  version "2024.12.31"
  resolved "https://registry.yarnpkg.com/@fawazahmed0/currency-api/-/currency-api-2024.12.31.tgz#bf06e3a837f5f52cb11c2f7d270b8517cc22b130"
  integrity sha512-ni8enzfzaCYryEKjiXYTyDmOM86IZONxOYkkKedJfhv6m0y2bdCIeAMfHtcaCSUJC3+MWdkmYZx3VWp91aK7Nw==

"@googlemaps/js-api-loader@^1.16.8":
  version "1.16.10"
  resolved "https://registry.yarnpkg.com/@googlemaps/js-api-loader/-/js-api-loader-1.16.10.tgz#b61cf2b4a1b6ed77017b0dab131e6d51e814ec4e"
  integrity sha512-c2erv2k7P2ilYzMmtYcMgAR21AULosQuUHJbStnrvRk2dG93k5cqptDrh9A8p+ZNlyhiqEOgHW7N9PAizdUM7Q==

"@googlemaps/markerclusterer@^2.5.3":
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/@googlemaps/markerclusterer/-/markerclusterer-2.6.2.tgz#b1b86868b4d9437a3e1218f7f63ca594ba868ffb"
  integrity sha512-U6uVhq8iWhiIckA89sgRu8OK35mjd6/3CuoZKWakKEf0QmRRWpatlsPb3kqXkoWSmbcZkopRiI4dnW6DQSd7bQ==
  dependencies:
    "@types/supercluster" "^7.1.3"
    fast-equals "^5.2.2"
    supercluster "^8.0.1"

"@gormartsen/vue-dataset@^1.0.1":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/@gormartsen/vue-dataset/-/vue-dataset-1.0.1.tgz#ef572029d17095de334ccd3a288daecca12f16b6"
  integrity sha512-TmI8uvVCf6evLIKuKyi38DHrhbKQYSwhoRVij4UR9bimJiB+/ps42wMxKGZ7XMXMwpE0eA2QSJe//14W0xJQBQ==

"@gormartsen/vue-debug@^1.0.0":
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/@gormartsen/vue-debug/-/vue-debug-1.0.0.tgz#46472b330f060a42b6cfb5a7af13b681ba277e45"
  integrity sha512-DvDtmJV9y5k5PM66ABNrVtWD/gJPll+jAEP42rl5SH1CDQ6RkPeaevHdXEvWnvkX8MWuwHDsJkMeGSBfj180LQ==

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "https://registry.yarnpkg.com/@humanwhocodes/config-array/-/config-array-0.13.0.tgz#fb907624df3256d04b9aa2df50d7aa97ec648748"
  integrity sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==

"@incomnetworking/agent-app-helper-functions@^1.0.0", "@incomnetworking/agent-app-helper-functions@^1.0.10", "@incomnetworking/agent-app-helper-functions@^1.0.2", "@incomnetworking/agent-app-helper-functions@^1.0.7":
  version "1.0.13"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/agent-app-helper-functions/1.0.13/d68ed763139fdf0d72c816eff82bfe77939d0275#d68ed763139fdf0d72c816eff82bfe77939d0275"
  integrity sha512-0QFfz8v3FREtTCZNk/7Fs/d5+deXJJLXtbHGLLCX2C636rscxs8HkGD3H2Sf2CPkMdvSyE4lUQcrHUqKeXRgAw==
  dependencies:
    "@incomnetworking/agent-app-helper-functions" "^1.0.2"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    maska "^3.1.1"

"@incomnetworking/agent-app-tabs-controler-store@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/agent-app-tabs-controler-store/1.0.2/d7ae88c877a04f5ffc4d2843942e46033534dfd1#d7ae88c877a04f5ffc4d2843942e46033534dfd1"
  integrity sha512-WMgiQ4erlBOjV/Tawif8QuitamhQrBeoaYSt7fRKi2xrQt2KfWgXfDbeVNv4tT2rOte8kvyVajyXD3f0uuZEdw==
  dependencies:
    pinia "^3.0.2"

"@incomnetworking/app-agent-activity-log-store@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-activity-log-store/1.0.0/7e1d2db0c0395602f91953b18b065946e4ce7a97#7e1d2db0c0395602f91953b18b065946e4ce7a97"
  integrity sha512-dz2SaBmGnOGUJxWeLmJKrMuiU5rbuP2Viag7MPk9lAjP4GjBeEhg7TPlbcLQLdo4LYIwEPJz60b3YPxFWk1i9g==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-address-lookup-modal-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-address-lookup-modal-vue-component/1.0.0/1623c34246fc2b1928470a1d2940aebb1d44ae8d#1623c34246fc2b1928470a1d2940aebb1d44ae8d"
  integrity sha512-StqhzS4l0f8e8nQDMxo3Zdpb5QDD9RoLXgBpFUenxEj3rZ9EtloFrgua4m+gMHX9pQWP+pYBJr6vlLPUjKfaGg==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.15"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-agent-store@^1.0.0", "@incomnetworking/app-agent-agent-store@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-agent-store/1.0.1/abdb438bd0af76a22d6d6886e4b49c03c5b4456d#abdb438bd0af76a22d6d6886e4b49c03c5b4456d"
  integrity sha512-Aps2zMpop2S5BUsSOhjuGA1vetaZgzRM513c34juUud5igI9MwKU/oHsMjyrNBrPVJ41MbsQB0JCDOJ1wod32Q==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-auth-store@^1.0.0":
  version "1.0.6"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-auth-store/1.0.6/1532ef4e2bf678e972334c098c2fb669636b114f#1532ef4e2bf678e972334c098c2fb669636b114f"
  integrity sha512-ema129NJ5v2F4bx6gUk6HYaeMTYzgmJJUz8QBbaaJkmsuEWSZg19FrRQIQ4LyguGPron+I/T6sri4s3+UX4d0w==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-agent-store" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-canned-message-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-canned-message-vue-component/1.0.1/014cb8da9d83bc225df38ac98ff14183e3329adf#014cb8da9d83bc225df38ac98ff14183e3329adf"
  integrity sha512-10WUs6c86KYE07CdPHCWaQVaUYePsRasbHm9eWgw2Ic1bnIqovto2gxdKZ1LpoiTU1wh9d0Gpxs1Doy3TGTnZA==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-email-limit-pricing-modal-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-email-limit-pricing-modal-vue-component/1.0.1/ae7f75f26a886a8d0749cd204b88f2651854d411#ae7f75f26a886a8d0749cd204b88f2651854d411"
  integrity sha512-MqZgIDjUwXHBuMsBWjCSGbd3U+a8mexFuVIfqObsY/gZg+Pp5SHp8SymeNdBfmT40GDkOy55w66LggwbE5AtFw==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-environment@^1.0.2":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-environment/1.0.3/41c4015110cab977f08ac3b2db5f4e7dad27f703#41c4015110cab977f08ac3b2db5f4e7dad27f703"
  integrity sha512-D4YVccqslf8Hw/uFrQfmMu6z6Eq/ZwzvQZcQYFo811FO2gUnfqE5GHuclvWz/t5eMjWvRszeRfPYm+Kymzu+Ag==
  dependencies:
    "@googlemaps/js-api-loader" "^1.16.8"

"@incomnetworking/app-agent-fonts-scss@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-fonts-scss/1.0.0/2b2e0cea107c9268631cbb42b4d126d815fb566a#2b2e0cea107c9268631cbb42b4d126d815fb566a"
  integrity sha512-gV2GOp4KE1ZA5YPCRphhv/R7mmIWiIZhmDM2g0SdLVGM1eehnwnw8hvFuL570RSK2/54iZ4LFUoo4CypdDPP5A==

"@incomnetworking/app-agent-get-access-token@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-get-access-token/1.0.0/5c26dbea24b66e782c4886c0a0039a18ac68d6b1#5c26dbea24b66e782c4886c0a0039a18ac68d6b1"
  integrity sha512-QXGlD7fiJwIyiwY7Hm3CyqdAy7fbkYa1ogHns8iUsAtKQMNZxh0mcV6ymYBMxYqfHVaYQT8zQNa/czE9xCkx/Q==

"@incomnetworking/app-agent-global-preferences-store@^1.0.1":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-global-preferences-store/1.0.2/43bebef0bba71781c183fbc3a41b6bdd72f410d7#43bebef0bba71781c183fbc3a41b6bdd72f410d7"
  integrity sha512-0oFfDckc9bafYq9T92RkDfDtce+hXStSmRcJj1ydmkpXsXGm0R8mH29H3Be/ddqiwwg7zMb4gS0SmHONIZ5uqw==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-global-scss@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-global-scss/1.0.0/b76ad789029c5485e64a4004309d537ff8d026fe#b76ad789029c5485e64a4004309d537ff8d026fe"
  integrity sha512-jLnWQKljm4ll84WOySVJo/6XQN9d+RYkOj9Xb7DGAJqV45gvhJGcBouEY7nqUoLBma6RJfV79OvFmptB4Gwv+w==

"@incomnetworking/app-agent-interface-styles@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-interface-styles/1.0.0/a763e4a78494c6636b5fee92224c1ffaf9333b83#a763e4a78494c6636b5fee92224c1ffaf9333b83"
  integrity sha512-Zd+MjTO16z71IqQXXKpzyvOgljeOiiwrO/XWu4ykX5pzbXzP3VJZAcpsP1BQEFjqIMjUwdpoqyWLlhiGUblqrQ==
  dependencies:
    bootstrap "^5.2.1"

"@incomnetworking/app-agent-lead-choose-modal-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-choose-modal-vue-component/1.0.1/f470eb7200612b025697a7c9647dfdcab7c781d7#f470eb7200612b025697a7c9647dfdcab7c781d7"
  integrity sha512-EQ7xPJ6GDsWxX6h3It+KZJWIocH9m3BPmd65IdI46LcsJh0/XIxqsyHcMbAc721/K09es5fWO6BjU8QNjSQmJA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-form-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-form-vue-component/1.0.1/7ae107786e655446bd736d405af1610db52afb57#7ae107786e655446bd736d405af1610db52afb57"
  integrity sha512-9KcpEIIY4oEZtQR+A24xZFk0TIGkn41mPJeRY+w6u2j7Xzv7+Hke+Ls5Hj+cQlhLo9RGS3UW5gonJ3wte0+YDQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-item-card-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-item-card-vue-component/1.0.0/5bb3e55a7d5d056920deca375ba506b310340b69#5bb3e55a7d5d056920deca375ba506b310340b69"
  integrity sha512-x91hgnHiwcL8OaSQNX4yd8K1nSWavvR9DCZ+F0vXiD5WCTsBkNNLMucg8lIsYnbNoM0rg1g9DQSShbo3l9+aRw==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-action-button-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-action-button-vue-component/1.0.0/9b63fe5859d55a59b76b9782b474a398a6dbf653#9b63fe5859d55a59b76b9782b474a398a6dbf653"
  integrity sha512-7XlR2DHcV7iWFWW9ZjvagpMVz8UmXcrIN6Uc0tM9/8k6rGyyTsnLunvY/EjMwO6N3qiajP/aMTgFSmH36B9ToQ==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component/1.0.1/dc6a5b8efe25a0eb04fc8a0fb300e2f0d842b5e9#dc6a5b8efe25a0eb04fc8a0fb300e2f0d842b5e9"
  integrity sha512-qMeWGD7cbO/TuLO435MQX802akOviRT1Rb012OTbvl2LIdNY/sfmzOIs/m0In10OQzCYu54/ibY1bf3QzLWY5w==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.15"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-confirm-delete-modal-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-confirm-delete-modal-vue-component/1.0.0/fe72774fb0e826820dcbe243c05045f964f5e101#fe72774fb0e826820dcbe243c05045f964f5e101"
  integrity sha512-4ckMG5bTnHXctQhTlvau4maWJwnOfSkPudHqhqWz5qXU/0XF3vCWSfewAXT+aRBsrRg5dLyra3obzUkAQM23TA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-data-view-mode-vue-component@^1.0.1":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-data-view-mode-vue-component/1.0.4/79cd3832dd49b2fd7fb0c044f72650cb732be317#79cd3832dd49b2fd7fb0c044f72650cb732be317"
  integrity sha512-kyHTVtKpc0XmJ8k+CJLPDES8dRVfsUaIpnDFSS7L3GQlp5dPAHz8JRu+YZrOAI3qyprLMHcF9/G6uKIlz3RSZw==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-notification-settings-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.1"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    events "^3.3.0"
    lodash "^4.17.21"
    pinia "^3.0.2"
    util "^0.12.5"
    xss "^1.0.15"

"@incomnetworking/app-agent-lead-profile-half-modal-vue-component@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-half-modal-vue-component/1.0.2/8ee5d59857e7c48cd28bf344af18a40fd366882b#8ee5d59857e7c48cd28bf344af18a40fd366882b"
  integrity sha512-GnAC4rU1M9RoaVGmeJRbvQpb7lCge23lS8s/U30NxL4LSmWwYd6Vmr785qVMPPfRxCTl0f/xVYSoUBnDIz3uSg==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-data-view-mode-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-lead-status-indicator-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    events "^3.3.0"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-header-vue-components@^1.0.0", "@incomnetworking/app-agent-lead-profile-header-vue-components@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-header-vue-components/1.0.2/0c895881c0081e7edf1bc51c77ef590fb18bebd0#0c895881c0081e7edf1bc51c77ef590fb18bebd0"
  integrity sha512-8oeacadF43LlUYNw2frN+nlIwScp4npL+OO75pLCi+DnYipZIpxITH5lyL8ITF3dkc29cQm4uuVuGoO7r46SVw==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component@^1.0.0", "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component/1.0.2/09200932954093a8bc7d277125b5fa884b09cb35#09200932954093a8bc7d277125b5fa884b09cb35"
  integrity sha512-ZnVobKRlAev8Ngaw7TtVJ3QwtQITB4tpLLpkh/G8NL7nJOMFf8/Cfo90U+DY7VX2rkfZ1mB8L4Du/NLntb3lBQ==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-action-button-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-listings-store" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-notification-settings-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    events "^3.3.0"
    lodash "^4.17.21"
    pinia "^3.0.2"
    util "^0.12.5"

"@incomnetworking/app-agent-lead-status-indicator-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-status-indicator-vue-component/1.0.0/9c44daea96f2e9b79ffc4c3f68c47c1e1a631af8#9c44daea96f2e9b79ffc4c3f68c47c1e1a631af8"
  integrity sha512-wlCnA3E3AIVScmztxzJwx0D05MYPYChIMaX0UZliC5LCtsHMzzENJyFHMMIQ62eke9g0ctJaEPox2K49PFmiHQ==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-lead-store@^1.0.0", "@incomnetworking/app-agent-lead-store@^1.0.2":
  version "1.0.5"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-lead-store/1.0.5/e0467ccaac8de55ecea0d865678af3be2f1d8a0d#e0467ccaac8de55ecea0d865678af3be2f1d8a0d"
  integrity sha512-Ttzwx3v2BSLsKvd3Ykw7bmoG5Y/OejycblyywKBFN2TzQ4OOxeiKyQ9oVKITczPrebdzTNrptfdq6QV6uyzpVA==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"
    lodash-es "^4.17.21"

"@incomnetworking/app-agent-leads-filter-form-vue-component@^1.0.0":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-leads-filter-form-vue-component/1.0.3/bf4cc23218c7927046e501d557e72011a3c8ddd9#bf4cc23218c7927046e501d557e72011a3c8ddd9"
  integrity sha512-u5/AxPpBNQcKqC7xd9tujnM8ZQ45DOyUszItna6/Rkzf5lAdBLYFA3kIix4WG9dilTrJv4dk9h755G4FewzaqQ==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-agent-store" "^1.0.1"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-lead-store" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-service-leads" "^1.0.1"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-leads-filter-modal-vue-component@^1.0.1":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-leads-filter-modal-vue-component/1.0.3/d41cc370cfded407c35182823671442f29c251d5#d41cc370cfded407c35182823671442f29c251d5"
  integrity sha512-K6IZMuPA7KSoPNUWa2p1GbeOwCeW6WgXaMtJ+tD/9GGUN/FCIW47oPp8GCQS8MnSQNHHUewCXNJG9pvY4MoLxw==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-leads-list-vue-component@^1.0.0", "@incomnetworking/app-agent-leads-list-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-leads-list-vue-component/1.0.1/a0d865fd56316d349869801fa5d3cc7aa6e328dd#a0d865fd56316d349869801fa5d3cc7aa6e328dd"
  integrity sha512-cekMcdOUb4HBlZxVbKV2NeCkk88aEgK4SPbVvhde9EVVSvMDbD1kAI9WNGcfBq1eNvkicbVzd0g30vR4ZlDAAA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-item-card-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-confirm-delete-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-sms-phone-or-app-actionsheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"
    util "^0.12.5"

"@incomnetworking/app-agent-listing-card-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-listing-card-vue-component/1.0.1/d0e53e2b956e5f875a791ba513f80bc69f6dff7f#d0e53e2b956e5f875a791ba513f80bc69f6dff7f"
  integrity sha512-BSSrGvppvDxF50tp7f0DqWa3xuspe7g4YdODgO7QFdPBa9VzJUNTQ4RWJU8lCeqHmp3f0nAlq2HN9R5jloUCdQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-listings-list-item-vue-component@^1.0.1", "@incomnetworking/app-agent-listings-list-item-vue-component@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-listings-list-item-vue-component/1.0.2/db065c01a14a22735aacd94181d1db829cbcdbaf#db065c01a14a22735aacd94181d1db829cbcdbaf"
  integrity sha512-SvlZTHqhdbbYyvhnyIMDNUXroMVacZdM9fCXbW1UxxrPxJ7cq2RfHFPCt17tSTvhoxZ4TJUGHW9Dw5etqFSnow==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-listings-store@^1.0.0", "@incomnetworking/app-agent-listings-store@^1.0.2":
  version "1.0.5"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-listings-store/1.0.5/6b898d86861f17d07dbea2217ee8e2445e829c24#6b898d86861f17d07dbea2217ee8e2445e829c24"
  integrity sha512-zMKemmz1Z7joiogKH1nRo0DcFX3+Yf1R3pymGN87bTQcOYC56WkkLB9bBhxL6onlfefly5KT6RT1a2IE6+Ecfw==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"
    dayjs "^1.11.13"

"@incomnetworking/app-agent-main-menu-vue-component@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-main-menu-vue-component/1.0.1/3d802a1e3dfc0ea08a211c459d09a9808777f1d8#3d802a1e3dfc0ea08a211c459d09a9808777f1d8"
  integrity sha512-va/zCVX9/bg0u67i4yIGOg+7TrwAmxWi11eqO/8iigMFahW8I0sZ0PUcBgS1JAk07rUD7DsK1u6z+sNJB2s7vQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-mass-message-action-sheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.2"
    "@incomnetworking/app-agent-view-saved-filters" "^1.0.4"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-mass-agent-message-modal-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-mass-agent-message-modal-vue-component/1.0.0/683c598fee2fac2885cb70df7ab9177fe3d057e4#683c598fee2fac2885cb70df7ab9177fe3d057e4"
  integrity sha512-63X0iZKtaObvD+S1vu/08mLe34V4w5tnnG5HDd5rjs5YzkGLpvBxGUy9yYIWnHEnnNgQVvJfe4SqPTNOWM+4oQ==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.15"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-mass-message-action-sheet-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-mass-message-action-sheet-vue-component/1.0.0/f16630e6c2bf44977a87f38e5e60dd9e9eae8c97#f16630e6c2bf44977a87f38e5e60dd9e9eae8c97"
  integrity sha512-/WRsTtmvutTM+7rNKirm9v5j3M24m/nCdskxzblVkHxTSxLOhAwsGTKn4uPejXzuyyvzcDDzGwag+Qxf6vOrug==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-mass-message-modal-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-mass-message-modal-vue-component/1.0.0/8b4ee0c03849afa478de6b5bae5b6711e3c506b1#8b4ee0c03849afa478de6b5bae5b6711e3c506b1"
  integrity sha512-0R4NS7bo4X1cMBq+bhQUu+AS+PnSzSpH7OfIka97u6fTtMPzdn9215mYoEoMxIO+fe7q+IIRbx+VHe0pBxC96A==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-form-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-saved-filter-store" "^1.0.0"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.15"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-message-store@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-message-store/1.0.1/fa113bf544437cad8a56b4f5e80967d9952f37bb#fa113bf544437cad8a56b4f5e80967d9952f37bb"
  integrity sha512-mTmn0a5wK7H/f+34mjDZowH4htjoaoKuyoUmu8EhV6pltDFMFTikwNm/yh5qE/+rl/ipnPTtfK6WeVCBiu+l0A==
  dependencies:
    "@capacitor/dialog" "^7.0.1"
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-types" "^1.0.1"
    capacitor-native-settings "^7.0.1"

"@incomnetworking/app-agent-multi-select-typehead-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-multi-select-typehead-vue-component/1.0.1/606713883010c2483fb2887df68fcfbd9218b224#606713883010c2483fb2887df68fcfbd9218b224"
  integrity sha512-7TJmgAK7VpLRS4VKeA0FPG24stj7ILyoJ0Co/swkzNxdH2pzS9BVBfxU+QyGhJHmD8PUxaIQ14zjVsJ8/717pA==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-notification-settings-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-notification-settings-vue-component/1.0.0/d3ed95bd0231152eec41e14f43c111a5856aa201#d3ed95bd0231152eec41e14f43c111a5856aa201"
  integrity sha512-hNwF0riPXK66Dis3+P8dGjvCqQUa6uP+bR1/MuE403X+roIKBbUpAUhrIvdqERy7EH+455YCYsGZs0zAhSjcDg==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-canned-message-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-picker-with-label" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-notifications-store@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-notifications-store/1.0.1/dc92027c9e1872d76b86b316a930cc106ddf971d#dc92027c9e1872d76b86b316a930cc106ddf971d"
  integrity sha512-Hc2ENitRui8GUBdPxs68s3Hd0PBAFr1HahgDVYeDhJsP4yaxCxD6I/G8SODIWMe+vyVVu9O6gUgwHz/+a3uqZQ==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-profile-store@^1.0.0":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-profile-store/1.0.3/0e5c9bd5584b7c7d6ebc0a0f6aabb0cbd463cda5#0e5c9bd5584b7c7d6ebc0a0f6aabb0cbd463cda5"
  integrity sha512-4GSmSMdTQ7fXQxrLyqbK/IySVWOPLYcNYdVMq3HEILEUW8PhbD3HtJA9ovI4qqZqmCVpUQ0n/7jh/0HAuC/XVw==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-types" "^1.0.1"
    lodash-es "^4.17.21"

"@incomnetworking/app-agent-push-notification-store@^1.0.0":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-push-notification-store/1.0.2/9a923229fa39d205ecfc45aa027148ee92ad7e17#9a923229fa39d205ecfc45aa027148ee92ad7e17"
  integrity sha512-AVuJE7oxSh3Xx8GFn4cBBdHjvymyVujHRlTDumJsdPWmlR2JZRqmAu6T8Z4f2nBzLqswmTveOBUiGcKG0x934g==
  dependencies:
    "@anuradev/capacitor-audio-toggle" "2.0.6"
    "@capacitor/dialog" "^7.0.1"
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-service-dialer-call-log" "^1.0.1"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-task-store" "^1.0.0"
    "@incomnetworking/app-agent-twillio-store" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"
    "@mozartec/capacitor-microphone" "5.0.0"
    capacitor-native-settings "^7.0.1"
    socket.io-client "^4.8.1"
    twilio-client "^1.15.1"

"@incomnetworking/app-agent-save-search-lead-modal-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-save-search-lead-modal-vue-component/1.0.1/0f0dbad080fcb3dbc01c56ad83e386ea980ea916#0f0dbad080fcb3dbc01c56ad83e386ea980ea916"
  integrity sha512-8A8UPUaSA8JFLC9POZR9b9+CLXBBBkq3SBAJYsq6UEqkX5iR6KNr9KS33GOilBYtioQJMrU85cAD/VjPFkgm3w==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-save-search-modal-vue-component@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-save-search-modal-vue-component/1.0.1/aa3fd6e414ef8bb09703895b9023de4afd035447#aa3fd6e414ef8bb09703895b9023de4afd035447"
  integrity sha512-uh59gV1OFIyqGpMpXd3BqrFTiy2xy9TtGzoTVBM7zWsg70FSqJVCZ0VhI6SYSmPayC5S4oYCspDpFN3anBdeeQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-save-search-lead-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-saved-filter-store@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-saved-filter-store/1.0.1/41c18002001af6a286d025afbd36b1553ef19fdd#41c18002001af6a286d025afbd36b1553ef19fdd"
  integrity sha512-b/XavQtJjOwFdwo+Og14wnbiW7F6tT4O0SBViqnkWtvwaTFibLEk2vzR+d4rPT7g6YegvPL227fRaj4cEFjLvw==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-service-activity@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-activity/1.0.2/61e92ce31caf147e58f730cb8bf557cdedb9b6bd#61e92ce31caf147e58f730cb8bf557cdedb9b6bd"
  integrity sha512-r2cIhBt8+O5gNn41ZXr8id07DDss8974i/1sM8ttAck9cxa138jMJxN98tP92L98wgH6INmhpuQCpELSfNw+0g==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-dialer-call-log@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-dialer-call-log/1.0.1/ec4aaf0b49d8eaa41417bf04249be946387ec6f6#ec4aaf0b49d8eaa41417bf04249be946387ec6f6"
  integrity sha512-z/eQnALuaYa9S1MTW1Suu/6RMlgiqfQR4MeMppLV8gr49J8X1Cf0zta1bkQQBDCcI7ZbpflmNda5Co6CIO6aEg==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-leads@^1.0.0", "@incomnetworking/app-agent-service-leads@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-leads/1.0.1/a7e49bc22038bb945ab04ad049e0a93a43b8c659#a7e49bc22038bb945ab04ad049e0a93a43b8c659"
  integrity sha512-aZD/nClpcAlgHrInhqmd1aMM3UiNfZtxp/7TROSIAwqvChYWeRNcyjGq1PUfDDSbSTSjP2gJjIAJ9HyOmtSR2A==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    lodash-es "^4.17.21"

"@incomnetworking/app-agent-service-listings@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-listings/1.0.0/75402bd31dd81a7c6d4ee8aba814243a47cd79a5#75402bd31dd81a7c6d4ee8aba814243a47cd79a5"
  integrity sha512-yk9RNg1z66zykJqprSsUBFDYLVSVW+FgJz/dbqFoeOZT6rV68Z/eIo1W3u1HlCy3LgDo1SibjNrqVcKNZd6r2Q==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-messages@^1.0.1", "@incomnetworking/app-agent-service-messages@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-messages/1.0.2/b62c15e01366ff36554d62a21ff9b97241f503a8#b62c15e01366ff36554d62a21ff9b97241f503a8"
  integrity sha512-zk3XybzYWeuou++Lqvjw1omF/+Pp1YRyn4LkTEn2n1jTgi2SZkfohTc7U8ttrBtSihI1A4Uv0qlbnseWApW1ig==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    lodash-es "^4.17.21"

"@incomnetworking/app-agent-service-mobileappsettings@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-mobileappsettings/1.0.2/dfc0d8454729e751192f6cb803cd5f1ef2317a7f#dfc0d8454729e751192f6cb803cd5f1ef2317a7f"
  integrity sha512-4ZhKHydic+G40uS2TCXJdQjN2lCh79kNOSOVa5E09vISPYjNxF1NR6zFSfFemLNmJCTz6JkFFSBTVgE/RUBKzQ==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-notifications@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-notifications/1.0.0/38a148c996203c2bae5855584cd36aad6d3a02f5#38a148c996203c2bae5855584cd36aad6d3a02f5"
  integrity sha512-yd5ioWh/Ow53I8D7482OrzQCrVo+p9s0pBmORo3NRX+vJV6RAbvcmPq8yTBBsN2cOOZEftvCOyBcF5k1H32J7w==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-tasks@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-tasks/1.0.0/3ae29a988bf98466204fdbdb5fb1d452fd259b78#3ae29a988bf98466204fdbdb5fb1d452fd259b78"
  integrity sha512-hAYKGg1ptuZp5hZSC/v7SjoB5AQS/T0gKaWZPNl9Wwd6bKEi294eg+t/l6nBvSIemUAi7ytfbE0vCSqDNNZHgQ==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-service-users@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-service-users/1.0.1/051a2ad2c1851545a5d1f5092b0d7315160f9316#051a2ad2c1851545a5d1f5092b0d7315160f9316"
  integrity sha512-a8KZBD8v3EebXt2BiZ8PIsj2JbGOq0XIqP6r4Pjt7rRwNX3pac7njn/KTQrC0aYt4/1KSWDpvg4eLqGMPEP8/g==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-sms-phone-or-app-actionsheet-vue-component@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-sms-phone-or-app-actionsheet-vue-component/1.0.1/06226f90b5edad38f664885a4b34e9080242f7f3#06226f90b5edad38f664885a4b34e9080242f7f3"
  integrity sha512-VlXkk41X8a18VjZ22S9fAlz+u4TsPyQQEbOAeoGQgx25TYoY0jPnnYsBVtccLs4t732z8bTNF0/Yd9VSBbrLMg==
  dependencies:
    "@byteowls/capacitor-sms" "^6.0.0"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-stepped-call-store@^1.0.0", "@incomnetworking/app-agent-stepped-call-store@^1.0.1":
  version "1.0.13"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-stepped-call-store/1.0.13/62530af5319930f7d07dda6ab83ff092d9aae7fa#62530af5319930f7d07dda6ab83ff092d9aae7fa"
  integrity sha512-SpGB9rAXHJ/U1UL0W4qYdGOhHl4i2vlazNr2Cr+tDiO32EVY+HVNE7sCXkGNMjCKi+0jvjUSjJEuOgYsibFhUw==
  dependencies:
    "@anuradev/capacitor-audio-toggle" "2.0.6"
    "@capacitor/dialog" "^7.0.1"
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-service-dialer-call-log" "^1.0.1"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-task-store" "^1.0.0"
    "@incomnetworking/app-agent-twillio-store" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"
    "@mozartec/capacitor-microphone" "5.0.0"
    capacitor-native-settings "^7.0.1"
    socket.io-client "^4.8.1"
    twilio-client "^1.15.1"

"@incomnetworking/app-agent-task-icon-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-task-icon-vue-component/1.0.0/bd88c8dc7b2b86fc1f0b7dcd09c5ca4b2bc41510#bd88c8dc7b2b86fc1f0b7dcd09c5ca4b2bc41510"
  integrity sha512-AFPT62/k1heZczyt1/WY/4fugG1nmbY+EsQ0y3qidFIdBwDDW9UnpeNGvj6lKCfI4U69GqVdKt1nQUOW518ubw==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-task-item-vue-component@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-task-item-vue-component/1.0.0/d3065bfc31a9c16fdd34dfb0503aa8a345df2a9d#d3065bfc31a9c16fdd34dfb0503aa8a345df2a9d"
  integrity sha512-JFod6GEVGIFjOp/ANLQcx6Rc/LPCCZMgRXf2wqkeOvrIO5cO85U/N1Zo0loGCpZg8ugwaS/F2/Z3mzGuaGY0rw==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^5.0.5"
    "@capawesome-team/capacitor-file-opener" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.0"
    "@incomnetworking/app-agent-lead-status-indicator-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-task-icon-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-task-store@^1.0.0":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-task-store/1.0.3/7cb660ad4505567c75bc5df2bf9b89616b73ce31#7cb660ad4505567c75bc5df2bf9b89616b73ce31"
  integrity sha512-0DFtwAFsV/zuS67Uiz42nlGCWp/QbMyDaaSPgmOaJFNF5pvWP/2IbCDKOkvDTeklgj6wxjcnhC/jaEZkIrXJlQ==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-activity-log-store" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-lead-store" "^1.0.0"
    "@incomnetworking/app-agent-notifications-store" "^1.0.0"
    "@incomnetworking/app-agent-service-tasks" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-twillio-store@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-twillio-store/1.0.0/80d3422fab2a2da6c1738d78fff3f078a632030d#80d3422fab2a2da6c1738d78fff3f078a632030d"
  integrity sha512-dNkDPkOk/VjPuvn0VFJzMBhcznFHQ9UW9Za+GKDjqq3+DfXnwGh8a6pWclXSKZBmcR+7sMSKEG6KCtAtggLa3Q==
  dependencies:
    "@anuradev/capacitor-audio-toggle" "2.0.6"
    "@capacitor/dialog" "^7.0.1"
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-types" "^1.0.1"
    capacitor-native-settings "^7.0.1"
    socket.io-client "^4.8.1"
    twilio-client "^1.15.1"

"@incomnetworking/app-agent-types@^1.0.1", "@incomnetworking/app-agent-types@^1.0.15":
  version "1.0.17"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-types/1.0.17/a53009eb8ff5840855bd5d1c630643257bd65803#a53009eb8ff5840855bd5d1c630643257bd65803"
  integrity sha512-yjG1FHmdWJb22evzaAhDtcUjqEQqz6pMe20Cx3tRyI0cy3VVyFSGxiGmnXVxqMyjITbZOrY8myoLDKALxa6S/w==
  dependencies:
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"

"@incomnetworking/app-agent-user-canned-message-store@^1.0.0", "@incomnetworking/app-agent-user-canned-message-store@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-user-canned-message-store/1.0.1/8e1e636db9b94b77f210d9f543a737598b45f447#8e1e636db9b94b77f210d9f543a737598b45f447"
  integrity sha512-n8ybDCyhKUyY4vMu9SzeXp75iJsk3uqpde1B2fRu+lg+WKGiL2JQv2jYZbxA1HEQc3QrLYgUUvY4a8m0Zv2YTQ==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-user-config-store@^1.0.1", "@incomnetworking/app-agent-user-config-store@^1.0.2":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-user-config-store/1.0.3/2fd3557567d61e3ac8f4053bb4eaafe5a4764336#2fd3557567d61e3ac8f4053bb4eaafe5a4764336"
  integrity sha512-qPxnrRe7P7wfgM+DsFIDqiwc3Z0VuW+LMslRIwVYV4M9ZBydWZ2f7gi1Jl3RDl41RbnkmRmPaNegKyCe6NWeTg==
  dependencies:
    "@capawesome/capacitor-badge" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.0"
    "@incomnetworking/app-agent-environment" "^1.0.2"
    "@incomnetworking/app-agent-get-access-token" "^1.0.0"
    "@incomnetworking/app-agent-types" "^1.0.1"

"@incomnetworking/app-agent-view-activity-log-page@^1.0.0":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-activity-log-page/1.0.4/bbc47aece66bddd86df79e4736a16c2c49374248#bbc47aece66bddd86df79e4736a16c2c49374248"
  integrity sha512-GMfHHOCbHOB2nF2RX5q7e+eD5kjXjbVZ27Jhnay9vwULNBoFHkh1TUBZTGfYq2Y3uEF3dgeiPSNj11eeCzp/zA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-agent-store" "^1.0.1"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-choose-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-listings-store" "^1.0.2"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-save-search-lead-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-save-search-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/app-agent-view-send-message-layout" "^1.0.0"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-alert" "^1.0.4"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-add-lead-category-page@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-add-lead-category-page/1.0.0/90acbc72da71732c9a76e595bf83182106282d35#90acbc72da71732c9a76e595bf83182106282d35"
  integrity sha512-K5CPN1o3LJjP7klA11AcdcjYt6v2YZS5xB6YmkMHwPLTqX/yMJ4yiKDue3jxE947MEGxw20fXYjpL5bce/HDPA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-add-lead-page@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-add-lead-page/1.0.1/9afbd65ba0a38a0a0defd43f00cb6023bace37ec#9afbd65ba0a38a0a0defd43f00cb6023bace37ec"
  integrity sha512-zxb9fBELdunLuM8o9wvzJV07viDYlD5A/NdtBvq+RDbQYcl1dQo0R6LquVB5pI/nIK/YNzPGww/1a23id5m/3Q==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-form-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-email-view-page@^1.0.1":
  version "1.0.5"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-email-view-page/1.0.5/43a70d860183f7b385ef61b17fc840dc9754f202#43a70d860183f7b385ef61b17fc840dc9754f202"
  integrity sha512-+S6BolBzC6t58XkeT1UlDP2bWXp5+O697xxx6zkL9mSqr85P5EQHDdJztPILPMF3ufVIZFpDh5vEF1ON6DnPCQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-listing-card-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    emailjs-mime-parser "^2.0.7"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-lead-categories-page@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-lead-categories-page/1.0.0/69db5ead61df2d925349bc4ea73d2a33f1e2dd9a#69db5ead61df2d925349bc4ea73d2a33f1e2dd9a"
  integrity sha512-1Yy10xeQTc2ahwvKu6ehsOgagm7TmaLmTu1K1sHR4raGZx4Z3HO+idffy3vqOOnnOc+ddkGKv55drsnQOgvg4A==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-leads-page@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-leads-page/1.0.0/08d1799058392591747605472300efe96fa276a9#08d1799058392591747605472300efe96fa276a9"
  integrity sha512-OPGHInSJWeH4CkU0V3w/kvw4zw2Dx4TvgGt4nVejHZEE0EtcnG5HYwMu56XgGKrI7/XWfyO/VHFKrpXprRv7gw==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-mass-message-action-sheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.2"
    "@incomnetworking/app-agent-view-saved-filters" "^1.0.4"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-listing-details@^1.0.4":
  version "1.0.5"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-listing-details/1.0.5/72dd36fae7b3302e974285a373c0370eecb44717#72dd36fae7b3302e974285a373c0370eecb44717"
  integrity sha512-BjPWScK9YJrTw76R+E04Q5quSTg9sLL0RyjDmM6avnwNJeMyJMxDOdHxki5cahrisgcwXYx0+5neW7Fs+dPWcw==
  dependencies:
    "@capacitor/browser" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@capacitor/share" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-leads-filter-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-listings-list-item-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-listings-store" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"
    swiper "^11.2.8"

"@incomnetworking/app-agent-view-login-page@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-login-page/1.0.2/9ee9f60f968b854c685acb7d5a9feccf60ab79c8#9ee9f60f968b854c685acb7d5a9feccf60ab79c8"
  integrity sha512-8BouyqIJi0Xg5ziZ9tdw2M3OerGoGk45sj0FppcEFAQ7VF52F2ncaNi8oYycYkwg/PdVZxOfOkIS9zGBwfHRKQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-manage-lead-page@^1.0.2":
  version "1.0.12"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-manage-lead-page/1.0.12/f7c0c0da599120f1ea6ce6652bdedb6be7707bb5#f7c0c0da599120f1ea6ce6652bdedb6be7707bb5"
  integrity sha512-WScRLs6tq4djHDltqhRLncXwfPldMun8C//QatriXZXIMyMCc0QvnZ+p2+ooe219R8hBMPS1Tl23Gu5Wsz0qiA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-form-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-item-card-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-add-anniversary-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-confirm-delete-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-sms-phone-or-app-actionsheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@vueuse/components" "^13.3.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"
    util "^0.12.5"

"@incomnetworking/app-agent-view-manage-task-page@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-manage-task-page/1.0.1/c691df2b1b5fcf92580ee959906a973eb21f952d#c691df2b1b5fcf92580ee959906a973eb21f952d"
  integrity sha512-lkycDHtRfixLD8UbfJx3LygmOd7nCSZCgwVoYHnEaYWSGw2HwqUXE2GwaRf0whiKnuYZBk9+/5kWpa0s0b83Yw==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-choose-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-task-icon-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@proteansoftware/capacitor-start-navigation" "^5.0.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-manage-task-view@^1.0.1":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-manage-task-view/1.0.3/c7eb5a02df721ec429f61bc0d9607c79227df6d5#c7eb5a02df721ec429f61bc0d9607c79227df6d5"
  integrity sha512-ck6RGM+WSF9MtqeB5lit6Qk6c3ktbqkYOrddWp0cbPiZtauTwokq+j4zYYwsGXV9JO6O3r9UQYs1mvqoeKrF2A==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-task-icon-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@proteansoftware/capacitor-start-navigation" "^5.0.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-message-page@^1.0.1":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-message-page/1.0.4/e87270efc73047a8822a0a84d8dbc607d65558bc#e87270efc73047a8822a0a84d8dbc607d65558bc"
  integrity sha512-rStNly7s9FUgHOMnJxpUk82ivYs9Z4r2JJrV5R0qG54z04fJopkyQlDE9+YugthoXT/44/LD4QTUjCV1pql13g==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/keyboard" "^7.0.1"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-listing-card-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-alert" "^1.0.4"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    emailjs-mime-parser "^2.0.7"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-messages-page@^1.0.1":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-messages-page/1.0.2/f99ddeea08bb1241fbe8d8b5852b877cf3241b15#f99ddeea08bb1241fbe8d8b5852b877cf3241b15"
  integrity sha512-IoJNygr8HYKXhjntvcgzFAV3+CyIxHER4y/O8OzRGNAoHt7FtgePLB1YmfqjHIfWDB278qfWnueuYlQ90I9jdA==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/keyboard" "^7.0.1"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-canned-message-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-choose-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-mass-agent-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-action-sheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-sms-phone-or-app-actionsheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-user-canned-message-store" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/app-agent-view-email-view-page" "^1.0.1"
    "@incomnetworking/app-agent-view-message-page" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-alert" "^1.0.4"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-notes-page@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-notes-page/1.0.1/05f3541b3c9cb15ff2c6a11955620f9c8dfdc2d8#05f3541b3c9cb15ff2c6a11955620f9c8dfdc2d8"
  integrity sha512-h1CEpnfkYcwYwhIsXq9dVT+fxy09TwYbs6oI3L2o6GAekU5St0yXfv6jtismBTBG8d0APF5pYZqC+BNEzs/BMQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.7"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-office-listings-page@^1.0.0":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-office-listings-page/1.0.2/f3f45ddfc9a1dc14be46f94b06d169b496f9a0b2#f3f45ddfc9a1dc14be46f94b06d169b496f9a0b2"
  integrity sha512-h303eCuM4Y0dC4nRPf66ELkzLGmG7hT7t+PpjfrzS+Cye/OkwiXX0Nu0qiPyArCct7eBJJAL3ik8ZD1G0xjq7Q==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/geolocation" "^7.1.2"
    "@capacitor/preferences" "^7.0.1"
    "@capacitor/share" "^7.0.1"
    "@googlemaps/markerclusterer" "^2.5.3"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-email-limit-pricing-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-fonts-scss" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-listing-card-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-listings-list-item-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-listings-store" "^1.0.2"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-save-search-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-stepped-call-store" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/app-agent-view-listing-details" "^1.0.4"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    js-marker-clusterer googlemaps/markerclusterer
    lodash-es "^4.17.21"
    pinia "^3.0.2"
    swiper "^11.2.8"

"@incomnetworking/app-agent-view-saved-filters@^1.0.4":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-saved-filters/1.0.4/326c7c238cb2a141f74c6e5c47048c23ac4c2162#326c7c238cb2a141f74c6e5c47048c23ac4c2162"
  integrity sha512-ZOPmBz8l2Xd8cHqJ2LR9JLh/G1iiuZRBcMREX7fCqAbkamFRt3mtNleW+OSZxhf4ZEDZOAN3r3FVAslU7LpSOg==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-store" "^1.0.2"
    "@incomnetworking/app-agent-listing-card-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    emailjs-mime-parser "^2.0.7"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-saved-searches-page@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-saved-searches-page/1.0.1/ee569cf9497cd66acc5f65802f9723c75a674e6e#ee569cf9497cd66acc5f65802f9723c75a674e6e"
  integrity sha512-UtJCD216kKJuzvvzM8yZUuYrMZgHOXZyOxOnYr7tYcf+Npj2q0i7Z7fCb+zuuEHGk8kpPZ7kCsy6DHD/aa53eQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-store" "^1.0.2"
    "@incomnetworking/app-agent-listings-store" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-save-search-lead-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-service-leads" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/app-agent-view-office-listings-page" "^1.0.0"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-searches-page@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-searches-page/1.0.1/0c72e7c7a51314f91dc371570b2f208bc8238a6d#0c72e7c7a51314f91dc371570b2f208bc8238a6d"
  integrity sha512-A/LZea/77JKzR1HOmmOTNuwwQ6okfEB16PSJq/rzozrKet6QCQWbnCtB99K3me0xi9cNALmUCQGewa7hChpu6Q==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-store" "^1.0.2"
    "@incomnetworking/app-agent-listings-store" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-save-search-lead-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-service-leads" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/app-agent-view-office-listings-page" "^1.0.0"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-send-message-layout@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-send-message-layout/1.0.1/b5af64b290eeb983203dcf11ab0b6492dff39ed2#b5af64b290eeb983203dcf11ab0b6492dff39ed2"
  integrity sha512-xWfqQ0gNkU0CxGwju/3D3LpVbitNuoim3hqSpvFSqFbUfg/qAMfcVODPXuckIKqVqlk4dyZ4h5R0Q+CvQE1kzg==
  dependencies:
    "@capacitor/camera" "^7.0.1"
    "@capacitor/core" "^7.2.0"
    "@capacitor/keyboard" "^7.0.1"
    "@capacitor/preferences" "^7.0.1"
    "@capawesome/capacitor-file-picker" "^7.1.0"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-email-limit-pricing-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-listing-card-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-mass-agent-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-service-leads" "^1.0.1"
    "@incomnetworking/app-agent-service-listings" "^1.0.0"
    "@incomnetworking/app-agent-service-messages" "^1.0.2"
    "@incomnetworking/app-agent-service-users" "^1.0.1"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-alert" "^1.0.4"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    emailjs-mime-parser "^2.0.7"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-tabs-page@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-tabs-page/1.0.1/349bc1227463f76606857233550bae2c493494c5#349bc1227463f76606857233550bae2c493494c5"
  integrity sha512-ZTiVD/CjGnfID+C+qz4SbqbpW10GB06MjyKZ7DZgEJPRM3cl5gBL9c9UwkRYN5adYhbbB9f8TGMl+J3hEmzFNg==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/agent-app-tabs-controler-store" "^1.0.2"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-leads-list-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-mass-message-action-sheet-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-mass-message-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-message-store" "^1.0.0"
    "@incomnetworking/app-agent-multi-select-typehead-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.2"
    "@incomnetworking/app-agent-view-saved-filters" "^1.0.4"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/app-agent-view-tasks-page@^1.0.1":
  version "1.0.9"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/app-agent-view-tasks-page/1.0.9/3169e5c892966f20c780b3f1357cbddec3275ba7#3169e5c892966f20c780b3f1357cbddec3275ba7"
  integrity sha512-fx30QxrJxmYpD8WvsFjgYUamRzvoBdDPts7yTpltRSTLagRJwJvP32JZYGhElbspF/zXKPx7unGIuJOllCoHjQ==
  dependencies:
    "@capacitor/core" "^7.2.0"
    "@capacitor/preferences" "^7.0.1"
    "@incomnetworking/agent-app-helper-functions" "^1.0.10"
    "@incomnetworking/app-agent-address-lookup-modal-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-auth-store" "^1.0.0"
    "@incomnetworking/app-agent-global-preferences-store" "^1.0.1"
    "@incomnetworking/app-agent-lead-choose-modal-vue-component" "^1.0.1"
    "@incomnetworking/app-agent-lead-profile-half-modal-vue-component" "^1.0.2"
    "@incomnetworking/app-agent-lead-profile-header-vue-components" "^1.0.2"
    "@incomnetworking/app-agent-profile-store" "^1.0.0"
    "@incomnetworking/app-agent-push-notification-store" "^1.0.0"
    "@incomnetworking/app-agent-task-icon-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-task-item-vue-component" "^1.0.0"
    "@incomnetworking/app-agent-user-config-store" "^1.0.1"
    "@incomnetworking/vue-component-app-action-sheet" "^1.0.1"
    "@incomnetworking/vue-component-app-datepicker" "^1.0.0"
    "@incomnetworking/vue-component-app-terms-and-conditions" "^1.0.0"
    "@incomnetworking/vue-components-app-common" "^1.0.0"
    "@incomnetworking/vue-components-app-icons" "^1.0.2"
    "@proteansoftware/capacitor-start-navigation" "^5.0.0"
    lodash-es "^4.17.21"
    pinia "^3.0.2"

"@incomnetworking/ckeditor5-vue@^5.1.4":
  version "5.1.5"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/ckeditor5-vue/5.1.5/fe0d4134c556f0c0a107246ecca35138284dfb4e#fe0d4134c556f0c0a107246ecca35138284dfb4e"
  integrity sha512-8R+LtSO24sdzIaXIbKVHXCgnmHptuiXBvaM6xXL1xHz/cBWKLoFyH4qW7C3SViHu32EOR1SdT8RIfLSmzbZnlQ==

"@incomnetworking/interface-text@^1.0.23":
  version "1.0.23"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/interface-text/1.0.23/fd4fcf61339f4dd2ad75142d89ec644c34a3a750#fd4fcf61339f4dd2ad75142d89ec644c34a3a750"
  integrity sha512-AzUHOro6xi+i2aTbzCYxFjLuKY9CZUbbUB5CQEA4c8JSjwzKJTP5QoucGv0670a13YrnSQ+iLK8yLiBAbfUNmQ==
  dependencies:
    "@gormartsen/vue-debug" "^1.0.0"

"@incomnetworking/interface-translations@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/interface-translations/1.0.0/fbae862eb346c2fee668317b259e741412f29bc6#fbae862eb346c2fee668317b259e741412f29bc6"
  integrity sha512-n+pbBmI7bfpPxU5ogBO2dWsuePLauQWv6aUxZPv5f3yQ1aKi5nwxXcA+HJpodJ9idmYOzPWuyuQfInBfClgNxg==

"@incomnetworking/js-google-map-library@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/js-google-map-library/1.0.1/fa50d7e68c1fbaa15529f71252ea094a0e4408b0#fa50d7e68c1fbaa15529f71252ea094a0e4408b0"
  integrity sha512-L/3ZYB2h6u40BL+Eo6H/KQyGw3T7Js4TWn05VBe4kVlqQBMPPOjtF6r/jSr8qXa/6EBqlVPl8XQ8AaBFXkKd1A==

"@incomnetworking/js-library-currency-rate@^2024.7.8":
  version "2024.7.8"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/js-library-currency-rate/2024.7.8/ab9ee0fdd6e944366635dd795e413dc137aea3de#ab9ee0fdd6e944366635dd795e413dc137aea3de"
  integrity sha512-Y/fAxReDGNN1KUdPtzJkjJOGxkdbbvZ/DsTN0RUC8hUUk909+nmRdrY2il+2/14v7xeBs0XvDo/Ugn3AVMnRLg==
  dependencies:
    "@fawazahmed0/currency-api" "^2024.7.8"
    "@gormartsen/vue-debug" "^1.0.0"

"@incomnetworking/page-builder-library@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/page-builder-library/1.0.2/b81e2c775eea76d7fbfa227bc5b1024b4e015521#b81e2c775eea76d7fbfa227bc5b1024b4e015521"
  integrity sha512-Z/3RVy7hvVkU74XrEnseaYABEhkgGEVJ+W6WVpyxy5kh9gSWUD4KWWp+bGQm3gRt4P0kRglgwWJfzg6InkMSUg==

"@incomnetworking/pagebuild-api-settings-dashboard@^1.0.0":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/pagebuild-api-settings-dashboard/1.0.4/447ffeccc9816c8ff1998c2899a3d524bc0e7bc8#447ffeccc9816c8ff1998c2899a3d524bc0e7bc8"
  integrity sha512-XPNk77R++2nibJtm0ob5QxfzlWQtgRqP6lW11lAN/LA9LtK0MN9aEfvLk/Y2aDU/A7VgowUvX3amrAB0Dgp3Vg==

"@incomnetworking/pagebuild-cdn-settings@^1.0.1":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/pagebuild-cdn-settings/1.0.2/ad277f48b5fd64b32f2ed01bb41b35d29938c05d#ad277f48b5fd64b32f2ed01bb41b35d29938c05d"
  integrity sha512-4ZurCQygFCwNiojwTfu8hm4Dn4D75zMwbJOJ/Wt/jCVDiRyl/5JnhdPg2vs02B7cuS7zT4uHADi4qwqDCaGLuw==

"@incomnetworking/pagebuilder-ckeditor@^1.0.2":
  version "1.0.3"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/pagebuilder-ckeditor/1.0.3/4b23c27eb918d1df9170a093a04c2d8ff6533bda#4b23c27eb918d1df9170a093a04c2d8ff6533bda"
  integrity sha512-a0ZqHWMXhqiOtfZJqYYKkiWtNStBYHLqQmvtmCqdzIdHp+1e1JGhjajE5Zg382VZt1DZ9Kco8vek+sPcbJipMw==
  dependencies:
    "@incomnetworking/ckeditor5-vue" "^5.1.4"

"@incomnetworking/vue-component-app-action-sheet@^1.0.1":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-component-app-action-sheet/1.0.1/637b28fa56f59d53a517998bf9181465d22b2103#637b28fa56f59d53a517998bf9181465d22b2103"
  integrity sha512-Qq7TZUJaBFUa2qKwsdyZ3R72m8dJiSublf4jJ1g9cJKZH8Az0evOeG4MjmQ2P0LHVYrTkvBx9v31UylSf1HL3Q==

"@incomnetworking/vue-component-app-datepicker@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-component-app-datepicker/1.0.0/91b6ddf64fa88c7a548e5433008fd62a29800386#91b6ddf64fa88c7a548e5433008fd62a29800386"
  integrity sha512-uaHqhp6tmGc7cLumYV3ixD3uvCsZkRVAxV8kKmA3ngMTbJslf8bFW8GZsV35TYXrcW2lPlcSkC/PeuUJ7mPrsA==

"@incomnetworking/vue-component-app-picker-with-label@^1.0.0":
  version "1.0.1"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-component-app-picker-with-label/1.0.1/284c46215088914833c1738ad351fec1a606a49c#284c46215088914833c1738ad351fec1a606a49c"
  integrity sha512-ZZhA6XqkLbLSLuYIpyq2+jYYWk8TnWvJSWmAX6ywwG3eXOuKgzO09HEFGpO+bjI/PzttU2hbNBMjWFLHFaey7Q==

"@incomnetworking/vue-component-app-terms-and-conditions@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-component-app-terms-and-conditions/1.0.0/7fb7009b037cabd87074db1433fa420740e6ab61#7fb7009b037cabd87074db1433fa420740e6ab61"
  integrity sha512-lVlnhHYtpPRAR4a1ss4Y+qbg5q+bQN4VlDH2qUlQB8gwcuM0f+ixv9nm0KZxCFEFcYA3A4s5j09tJCpsR/CSpw==

"@incomnetworking/vue-components-app-alert@^1.0.4":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-components-app-alert/1.0.4/92fb58df1acca6440bd26fe360b737f9b6ad519f#92fb58df1acca6440bd26fe360b737f9b6ad519f"
  integrity sha512-TkPXbpUeFMfekcX3gCsRfLVS5eA0ifKZNjFoevhncYO+WXvVHlwcnZTntPUhV2nsvWSJW339Js1iN0obL8qLeg==

"@incomnetworking/vue-components-app-common@^1.0.0":
  version "1.0.0"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-components-app-common/1.0.0/553e5439b1524b90cd7707cc13764979dbb68280#553e5439b1524b90cd7707cc13764979dbb68280"
  integrity sha512-PFy/vPpYpyuKnKvKib7Il1re6DX66ZFEX/OC3rGTBhilKTt0zh33X2UIx5Pu6C1ymGhLrUe2dggHYiAHTpim/Q==
  dependencies:
    "@incomnetworking/vue-components-app-icons" "^1.0.2"

"@incomnetworking/vue-components-app-icons@^1.0.2":
  version "1.0.2"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-components-app-icons/1.0.2/108557c463491dfa0606714c0b7d678a21f28e37#108557c463491dfa0606714c0b7d678a21f28e37"
  integrity sha512-ZLzun6Mq0Q6ziujbxIRswNJgfPA/K2Jm8iy5yf9wop0eK6/E2ggrhhHY5OHEBHoanEH5jFJXpfHRM4SbxW30mw==

"@incomnetworking/vue-plugin-app-helper@^1.0.22":
  version "1.0.25"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-plugin-app-helper/1.0.25/032aeaeef454a5d81d830862f3ac6db3af1e66b5#032aeaeef454a5d81d830862f3ac6db3af1e66b5"
  integrity sha512-1fgdLVljT+JTIwvCRx7E8kgWCGNMEDI3ehw283Ir3XpM2vpPVZYG6dIcsQhQMqoFXcktWiqHdpGMwNfFkXbM2w==
  dependencies:
    "@gormartsen/vue-debug" "^1.0.0"
    "@incomnetworking/js-google-map-library" "^1.0.1"
    "@incomnetworking/js-library-currency-rate" "^2024.7.8"
    countries-list "^3.1.0"
    iso-country-currency "^0.7.2"

"@incomnetworking/vue-plugin-auth@^1.0.3":
  version "1.0.4"
  resolved "https://npm.pkg.github.com/download/@incomnetworking/vue-plugin-auth/1.0.4/c84a5baf4d73aa63f82eb732b539095d4a1bf4c7#c84a5baf4d73aa63f82eb732b539095d4a1bf4c7"
  integrity sha512-e/txc9GNDEwixTA4frr0Cl8Bt/qyZOmfw/7wTcBoHkpIlE3acoAw1yX5tIweBjTKRwGrmOV0xRvtwBCn0LMCCA==
  dependencies:
    "@gormartsen/vue-debug" "^1.0.0"

"@ionic/core@8.7.1":
  version "8.7.1"
  resolved "https://registry.yarnpkg.com/@ionic/core/-/core-8.7.1.tgz#2260974e86e2b49f9bd392f67d91bfd029b9702d"
  integrity sha512-TSJDPWayn23Dw0gjwvbumo6piDrpZvyVccgMUGyKDrqduvBogzIsPrjPBYfTF4z4Sc/W0HMad17nBskC2+ybqw==
  dependencies:
    "@stencil/core" "4.36.2"
    ionicons "^8.0.13"
    tslib "^2.1.0"

"@ionic/vue-router@^8.5.8":
  version "8.7.1"
  resolved "https://registry.yarnpkg.com/@ionic/vue-router/-/vue-router-8.7.1.tgz#da4f189af3730be2476c2f33d2fe324b1e9b78cf"
  integrity sha512-k+iku90EZ/VQswK0lFDq++Lq7GzDwIARMzp2e887ORsnfficaP7pBsIHB2BpLjzEtxClx0pyBMeqiFxvZiH/Gw==
  dependencies:
    "@ionic/vue" "8.7.1"

"@ionic/vue@8.7.1", "@ionic/vue@^8.5.8":
  version "8.7.1"
  resolved "https://registry.yarnpkg.com/@ionic/vue/-/vue-8.7.1.tgz#ae1d19c57097a308cd989e1fda09fd718ffe4a5a"
  integrity sha512-b/wIsactN870z1t+jRWEemtCtO5QwBg5e49ycWiOjHYPYZd7UBU1lRWSrvzbtMNvBEYbTTWBHg/ewGFL7EFxBw==
  dependencies:
    "@ionic/core" "8.7.1"
    "@stencil/vue-output-target" "0.10.7"
    ionicons "^8.0.13"

"@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://registry.yarnpkg.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz#7358043433b2e5da569aa02cbc4c121da3af27d7"
  integrity sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==

"@microservice-framework/microservice-client@github:microservice-framework/microservice-client#2.x":
  version "2.0.0"
  resolved "https://codeload.github.com/microservice-framework/microservice-client/tar.gz/51aea9d9669ab348d86fd6fe455a52912827c6d7"
  dependencies:
    axios "^1.6.7"
    debug "^4.3.4"

"@microservice-framework/vue-api-client@github:microservice-framework/vue-api-client#2.x":
  version "2.0.0"
  resolved "https://codeload.github.com/microservice-framework/vue-api-client/tar.gz/b12b866bd275e7a2eb0c8d2e6f7e3bcb07971e65"
  dependencies:
    "@microservice-framework/microservice-client" "github:microservice-framework/microservice-client#2.x"

"@mozartec/capacitor-microphone@5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@mozartec/capacitor-microphone/-/capacitor-microphone-5.0.0.tgz#39e7d27ad5a5a4942efdcb8ba06ca9e8877817d4"
  integrity sha512-XL7jIJc/wY0nuI6YIoqVOS8rHy7+OL2pz2PgiceW4CemcXjthoDWOdviK6kZwrIvF2cpreR6YSz+c41VSuhpig==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@parcel/watcher-android-arm64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz#507f836d7e2042f798c7d07ad19c3546f9848ac1"
  integrity sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==

"@parcel/watcher-darwin-arm64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz#3d26dce38de6590ef79c47ec2c55793c06ad4f67"
  integrity sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==

"@parcel/watcher-darwin-x64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz#99f3af3869069ccf774e4ddfccf7e64fd2311ef8"
  integrity sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==

"@parcel/watcher-freebsd-x64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz#14d6857741a9f51dfe51d5b08b7c8afdbc73ad9b"
  integrity sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==

"@parcel/watcher-linux-arm-glibc@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz#43c3246d6892381db473bb4f663229ad20b609a1"
  integrity sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==

"@parcel/watcher-linux-arm-musl@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz#663750f7090bb6278d2210de643eb8a3f780d08e"
  integrity sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==

"@parcel/watcher-linux-arm64-glibc@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz#ba60e1f56977f7e47cd7e31ad65d15fdcbd07e30"
  integrity sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==

"@parcel/watcher-linux-arm64-musl@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz#f7fbcdff2f04c526f96eac01f97419a6a99855d2"
  integrity sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==

"@parcel/watcher-linux-x64-glibc@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz#4d2ea0f633eb1917d83d483392ce6181b6a92e4e"
  integrity sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==

"@parcel/watcher-linux-x64-musl@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz#277b346b05db54f55657301dd77bdf99d63606ee"
  integrity sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==

"@parcel/watcher-win32-arm64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz#7e9e02a26784d47503de1d10e8eab6cceb524243"
  integrity sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==

"@parcel/watcher-win32-ia32@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz#2d0f94fa59a873cdc584bf7f6b1dc628ddf976e6"
  integrity sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==

"@parcel/watcher-win32-x64@2.5.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz#ae52693259664ba6f2228fa61d7ee44b64ea0947"
  integrity sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==

"@parcel/watcher@^2.4.1":
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/@parcel/watcher/-/watcher-2.5.1.tgz#342507a9cfaaf172479a882309def1e991fb1200"
  integrity sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.1"
    "@parcel/watcher-darwin-arm64" "2.5.1"
    "@parcel/watcher-darwin-x64" "2.5.1"
    "@parcel/watcher-freebsd-x64" "2.5.1"
    "@parcel/watcher-linux-arm-glibc" "2.5.1"
    "@parcel/watcher-linux-arm-musl" "2.5.1"
    "@parcel/watcher-linux-arm64-glibc" "2.5.1"
    "@parcel/watcher-linux-arm64-musl" "2.5.1"
    "@parcel/watcher-linux-x64-glibc" "2.5.1"
    "@parcel/watcher-linux-x64-musl" "2.5.1"
    "@parcel/watcher-win32-arm64" "2.5.1"
    "@parcel/watcher-win32-ia32" "2.5.1"
    "@parcel/watcher-win32-x64" "2.5.1"

"@proteansoftware/capacitor-start-navigation@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@proteansoftware/capacitor-start-navigation/-/capacitor-start-navigation-5.0.0.tgz#3188e170cc9df5e8a11f0fde660da79dd2b8e71b"
  integrity sha512-4mxqGcupfPZlCx3RGRySpZzWzLgvNXNo9SQmDxUc5FO0RJ5afNDivBHQIsQ5vLsU8WvSOU1H6fndAxCQBr15/A==

"@rollup/pluginutils@^4.0.0":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@rollup/pluginutils/-/pluginutils-4.2.1.tgz#e6c6c3aba0744edce3fb2074922d3776c0af2a6d"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rollup/rollup-darwin-arm64@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz#363467bc49fd0b1e17075798ac8e9ad1e1e29535"
  integrity sha512-0CY3/K54slrzLDjOA7TOjN1NuLKERBgk9nY5V34mhmuu673YNb+7ghaDUs6N0ujXR7fz5XaS5Aa6d2TNxZd0OQ==

"@rollup/rollup-darwin-x64@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz#c2fe3d85fffe47f0ed0f076b3563ada22c8af19c"
  integrity sha512-eOojSEAi/acnsJVYRxnMkPFqcxSMFfrw7r2iD9Q32SGkb/Q9FpUY1UlAu1DH9T7j++gZ0lHjnm4OyH2vCI7l7Q==

"@rollup/rollup-linux-arm64-gnu@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz#1015c9d07a99005025d13b8622b7600029d0b52f"
  integrity sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==

"@rollup/rollup-linux-arm64-musl@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz#8f895eb5577748fc75af21beae32439626e0a14c"
  integrity sha512-LD2fytxZJZ6xzOKnMbIpgzFOuIKlxVOpiMAXawsAZ2mHBPEYOnLRK5TTEsID6z4eM23DuO88X0Tq1mErHMVq0A==

"@rollup/rollup-linux-x64-gnu@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz#7193cbd8d128212b8acda37e01b39d9e96259ef8"
  integrity sha512-FwBHNSOjUTQLP4MG7y6rR6qbGw4MFeQnIBrMe161QGaQoBQLqSUEKlHIiVgF3g/mb3lxlxzJOpIBhaP+C+KP2A==

"@rollup/rollup-linux-x64-musl@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz#29a6867278ca0420b891574cfab98ecad70c59d1"
  integrity sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==

"@rollup/rollup-win32-arm64-msvc@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz#89427dcac0c8e3a6d32b13a03a296a275d0de9a9"
  integrity sha512-z4mQK9dAN6byRA/vsSgQiPeuO63wdiDxZ9yg9iyX2QTzKuQM7T4xlBoeUP/J8uiFkqxkcWndWi+W7bXdPbt27Q==

"@rollup/rollup-win32-x64-msvc@4.34.9":
  version "4.34.9"
  resolved "https://registry.yarnpkg.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz#1973871850856ae72bc678aeb066ab952330e923"
  integrity sha512-AyleYRPU7+rgkMWbEh71fQlrzRfeP6SyMnRf9XX4fCdDPAJumdSBqYEcWPMzVQ4ScAl7E4oFfK0GUVn77xSwbw==

"@rushstack/eslint-patch@^1.1.0":
  version "1.12.0"
  resolved "https://registry.yarnpkg.com/@rushstack/eslint-patch/-/eslint-patch-1.12.0.tgz#326a7b46f6d4cfa54ae25bb888551697873069b4"
  integrity sha512-5EwMtOqvJMMa3HbmxLlF74e+3/HhwBTMcvt3nqVJgGCozO6hzIPOBlwm8mGVNR9SN2IJpxSnlxczyDjcn7qIyw==

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz#821f8442f4175d8f0467b9daf26e3a18e2d02af2"
  integrity sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==

"@stencil/core@4.36.2", "@stencil/core@^4.35.3":
  version "4.36.2"
  resolved "https://registry.yarnpkg.com/@stencil/core/-/core-4.36.2.tgz#b43cba342c5c5ceba7c09463fdba7edff792496e"
  integrity sha512-PRFSpxNzX9Oi0Wfh02asztN9Sgev/MacfZwmd+VVyE6ZxW+a/kEpAYZhzGAmE+/aKVOGYuug7R9SulanYGxiDQ==
  optionalDependencies:
    "@rollup/rollup-darwin-arm64" "4.34.9"
    "@rollup/rollup-darwin-x64" "4.34.9"
    "@rollup/rollup-linux-arm64-gnu" "4.34.9"
    "@rollup/rollup-linux-arm64-musl" "4.34.9"
    "@rollup/rollup-linux-x64-gnu" "4.34.9"
    "@rollup/rollup-linux-x64-musl" "4.34.9"
    "@rollup/rollup-win32-arm64-msvc" "4.34.9"
    "@rollup/rollup-win32-x64-msvc" "4.34.9"

"@stencil/vue-output-target@0.10.7":
  version "0.10.7"
  resolved "https://registry.yarnpkg.com/@stencil/vue-output-target/-/vue-output-target-0.10.7.tgz#4087e6fc008338a107ee70277d6d04b3e8a59653"
  integrity sha512-IYxDe+SLCkwhwsWRdynE31rTK1zN3hVwwojQ/V9lrN8Gnx4PTvrUQHiRno9jFo1dk+EaBZWX9gZSmXta0ZaZew==

"@twilio/audioplayer@1.0.6":
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/@twilio/audioplayer/-/audioplayer-1.0.6.tgz#e29015fc38fa76c9163ea6145a8ff37fd550205d"
  integrity sha512-c9cjX/ifICgXqShtyAQdVMqfe7odnxougiuRMXBJtn3dZ320mFdt7kmuKedpNnc3ZJ6irOZ9M9MZi9/vuEqHiw==
  dependencies:
    babel-runtime "^6.26.0"

"@twilio/voice-errors@1.0.1":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/@twilio/voice-errors/-/voice-errors-1.0.1.tgz#be84159d05593ec0c6d353ede742340436cc3e9e"
  integrity sha512-iXzCuiOhNMhrr8DVjRRzI14YwGUIBM83kWSWcDktxmXim0Tz9xoCth4QFAQcMkNL2h9DlfXlob6noH+3h2iA4A==

"@types/estree@*":
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/@types/estree/-/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e"
  integrity sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==

"@types/geojson@*":
  version "7946.0.16"
  resolved "https://registry.yarnpkg.com/@types/geojson/-/geojson-7946.0.16.tgz#8ebe53d69efada7044454e3305c19017d97ced2a"
  integrity sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==

"@types/lodash-es@^4.17.12":
  version "4.17.12"
  resolved "https://registry.yarnpkg.com/@types/lodash-es/-/lodash-es-4.17.12.tgz#65f6d1e5f80539aa7cfbfc962de5def0cf4f341b"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.17.20"
  resolved "https://registry.yarnpkg.com/@types/lodash/-/lodash-4.17.20.tgz#1ca77361d7363432d29f5e55950d9ec1e1c6ea93"
  integrity sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==

"@types/supercluster@^7.1.3":
  version "7.1.3"
  resolved "https://registry.yarnpkg.com/@types/supercluster/-/supercluster-7.1.3.tgz#1a1bc2401b09174d9c9e44124931ec7874a72b27"
  integrity sha512-Z0pOY34GDFl3Q6hUFYf3HkTwKEE02e7QgtJppBt+beEAxnyOpJua+voGFvxINBHa06GwLFFym7gRPY2SiKIfIA==
  dependencies:
    "@types/geojson" "*"

"@types/web-bluetooth@^0.0.21":
  version "0.0.21"
  resolved "https://registry.yarnpkg.com/@types/web-bluetooth/-/web-bluetooth-0.0.21.tgz#525433c784aed9b457aaa0ee3d92aeb71f346b63"
  integrity sha512-oIQLCGWtcFZy2JW77j9k8nHzAOpqMHLQejDA48XXMWH6tjCQHz5RCFz1bzsmROyL6PUm+LLnUiI4BCn221inxA==

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/@ungap/structured-clone/-/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8"
  integrity sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==

"@vitejs/plugin-vue@^3.0.1":
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/@vitejs/plugin-vue/-/plugin-vue-3.2.0.tgz#a1484089dd85d6528f435743f84cdd0d215bbb54"
  integrity sha512-E0tnaL4fr+qkdCNxJ+Xd0yM31UwMkQje76fsDVBBUCoGOUPexu2VDUYHL8P4CwV+zMvWw6nlRw19OnRKmYAJpw==

"@vue/compiler-core@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/compiler-core/-/compiler-core-3.5.18.tgz#521a138cdd970d9bfd27e42168d12f77a04b2074"
  integrity sha512-3slwjQrrV1TO8MoXgy3aynDQ7lslj5UqDxuHnrzHtpON5CBinhWjJETciPngpin/T3OuW3tXUf86tEurusnztw==
  dependencies:
    "@babel/parser" "^7.28.0"
    "@vue/shared" "3.5.18"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/compiler-dom/-/compiler-dom-3.5.18.tgz#e13504492c3061ec5bbe6a2e789f15261d4f03a7"
  integrity sha512-RMbU6NTU70++B1JyVJbNbeFkK+A+Q7y9XKE2EM4NLGm2WFR8x9MbAtWxPPLdm0wUkuZv9trpwfSlL6tjdIa1+A==
  dependencies:
    "@vue/compiler-core" "3.5.18"
    "@vue/shared" "3.5.18"

"@vue/compiler-sfc@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/compiler-sfc/-/compiler-sfc-3.5.18.tgz#ba1e849561337d809937994cdaf900539542eeca"
  integrity sha512-5aBjvGqsWs+MoxswZPoTB9nSDb3dhd1x30xrrltKujlCxo48j8HGDNj3QPhF4VIS0VQDUrA1xUfp2hEa+FNyXA==
  dependencies:
    "@babel/parser" "^7.28.0"
    "@vue/compiler-core" "3.5.18"
    "@vue/compiler-dom" "3.5.18"
    "@vue/compiler-ssr" "3.5.18"
    "@vue/shared" "3.5.18"
    estree-walker "^2.0.2"
    magic-string "^0.30.17"
    postcss "^8.5.6"
    source-map-js "^1.2.1"

"@vue/compiler-ssr@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/compiler-ssr/-/compiler-ssr-3.5.18.tgz#aecde0b0bff268a9c9014ba66799307c4a784328"
  integrity sha512-xM16Ak7rSWHkM3m22NlmcdIM+K4BMyFARAfV9hYFl+SFuRzrZ3uGMNW05kA5pmeMa0X9X963Kgou7ufdbpOP9g==
  dependencies:
    "@vue/compiler-dom" "3.5.18"
    "@vue/shared" "3.5.18"

"@vue/devtools-api@^6.5.1":
  version "6.6.4"
  resolved "https://registry.yarnpkg.com/@vue/devtools-api/-/devtools-api-6.6.4.tgz#cbe97fe0162b365edc1dba80e173f90492535343"
  integrity sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==

"@vue/devtools-api@^7.7.2":
  version "7.7.7"
  resolved "https://registry.yarnpkg.com/@vue/devtools-api/-/devtools-api-7.7.7.tgz#5ef5f55f60396220725a273548c0d7ee983d5d34"
  integrity sha512-lwOnNBH2e7x1fIIbVT7yF5D+YWhqELm55/4ZKf45R9T8r9dE2AIOy8HKjfqzGsoTHFbWbr337O4E0A0QADnjBg==
  dependencies:
    "@vue/devtools-kit" "^7.7.7"

"@vue/devtools-kit@^7.7.7":
  version "7.7.7"
  resolved "https://registry.yarnpkg.com/@vue/devtools-kit/-/devtools-kit-7.7.7.tgz#41a64f9526e9363331c72405544df020ce2e3641"
  integrity sha512-wgoZtxcTta65cnZ1Q6MbAfePVFxfM+gq0saaeytoph7nEa7yMXoi6sCPy4ufO111B9msnw0VOWjPEFCXuAKRHA==
  dependencies:
    "@vue/devtools-shared" "^7.7.7"
    birpc "^2.3.0"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.2"

"@vue/devtools-shared@^7.7.7":
  version "7.7.7"
  resolved "https://registry.yarnpkg.com/@vue/devtools-shared/-/devtools-shared-7.7.7.tgz#ff14aa8c1262ebac8c0397d3b09f767cd489750c"
  integrity sha512-+udSj47aRl5aKb0memBvcUG9koarqnxNM5yjuREvqwK6T3ap4mn3Zqqc17QrBFTqSMjr3HK1cvStEZpMDpfdyw==
  dependencies:
    rfdc "^1.4.1"

"@vue/eslint-config-prettier@^7.0.0":
  version "7.1.0"
  resolved "https://registry.yarnpkg.com/@vue/eslint-config-prettier/-/eslint-config-prettier-7.1.0.tgz#97936379c7fb1d982b9d2c6b122306e3c2e464c8"
  integrity sha512-Pv/lVr0bAzSIHLd9iz0KnvAr4GKyCEl+h52bc4e5yWuDVtLgFwycF7nrbWTAQAS+FU6q1geVd07lc6EWfJiWKQ==
  dependencies:
    eslint-config-prettier "^8.3.0"
    eslint-plugin-prettier "^4.0.0"

"@vue/reactivity@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/reactivity/-/reactivity-3.5.18.tgz#fe32166e3938832c54b4134e60e9b58ca7d9bdb4"
  integrity sha512-x0vPO5Imw+3sChLM5Y+B6G1zPjwdOri9e8V21NnTnlEvkxatHEH5B5KEAJcjuzQ7BsjGrKtfzuQ5eQwXh8HXBg==
  dependencies:
    "@vue/shared" "3.5.18"

"@vue/runtime-core@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/runtime-core/-/runtime-core-3.5.18.tgz#9e9ae8b9491548b53d0cea2bf25746d27c52e191"
  integrity sha512-DUpHa1HpeOQEt6+3nheUfqVXRog2kivkXHUhoqJiKR33SO4x+a5uNOMkV487WPerQkL0vUuRvq/7JhRgLW3S+w==
  dependencies:
    "@vue/reactivity" "3.5.18"
    "@vue/shared" "3.5.18"

"@vue/runtime-dom@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/runtime-dom/-/runtime-dom-3.5.18.tgz#1150952d1048b5822e4f1dd8aed24665cbb22107"
  integrity sha512-YwDj71iV05j4RnzZnZtGaXwPoUWeRsqinblgVJwR8XTXYZ9D5PbahHQgsbmzUvCWNF6x7siQ89HgnX5eWkr3mw==
  dependencies:
    "@vue/reactivity" "3.5.18"
    "@vue/runtime-core" "3.5.18"
    "@vue/shared" "3.5.18"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/server-renderer/-/server-renderer-3.5.18.tgz#e9fa267b95b3a1d8cddca762377e5de2ae9122bd"
  integrity sha512-PvIHLUoWgSbDG7zLHqSqaCoZvHi6NNmfVFOqO+OnwvqMz/tqQr3FuGWS8ufluNddk7ZLBJYMrjcw1c6XzR12mA==
  dependencies:
    "@vue/compiler-ssr" "3.5.18"
    "@vue/shared" "3.5.18"

"@vue/shared@3.5.18":
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/@vue/shared/-/shared-3.5.18.tgz#529f24a88d3ed678d50fd5c07455841fbe8ac95e"
  integrity sha512-cZy8Dq+uuIXbxCZpuLd2GJdeSO/lIzIspC2WtkqIpje5QyFbvLaI5wZtdUjLHjGZrlVX6GilejatWwVYYRc8tA==

"@vueuse/components@^13.3.0":
  version "13.6.0"
  resolved "https://registry.yarnpkg.com/@vueuse/components/-/components-13.6.0.tgz#0b7550518588857b10407cebb5e33c1c81101cf8"
  integrity sha512-wJmXlQhCRWBqpnRW4KVbbKqCW28AZ/5hoA/gy7cvTXJUiRdKKfjHFo+7MGwn80dFfIkCA5253T7Y3fbNK4/lUw==
  dependencies:
    "@vueuse/core" "13.6.0"
    "@vueuse/shared" "13.6.0"

"@vueuse/core@13.6.0":
  version "13.6.0"
  resolved "https://registry.yarnpkg.com/@vueuse/core/-/core-13.6.0.tgz#4137f63dc4cef2ff8ae74ee146d6b6070d707878"
  integrity sha512-DJbD5fV86muVmBgS9QQPddVX7d9hWYswzlf4bIyUD2dj8GC46R1uNClZhVAmsdVts4xb2jwp1PbpuiA50Qee1A==
  dependencies:
    "@types/web-bluetooth" "^0.0.21"
    "@vueuse/metadata" "13.6.0"
    "@vueuse/shared" "13.6.0"

"@vueuse/metadata@13.6.0":
  version "13.6.0"
  resolved "https://registry.yarnpkg.com/@vueuse/metadata/-/metadata-13.6.0.tgz#49196025c96c7daeb591c20a54b61cc336af99b6"
  integrity sha512-rnIH7JvU7NjrpexTsl2Iwv0V0yAx9cw7+clymjKuLSXG0QMcLD0LDgdNmXic+qL0SGvgSVPEpM9IDO/wqo1vkQ==

"@vueuse/shared@13.6.0":
  version "13.6.0"
  resolved "https://registry.yarnpkg.com/@vueuse/shared/-/shared-13.6.0.tgz#872fdbd725fb4e3a12bd5aab85af9a5db0b1e481"
  integrity sha512-pDykCSoS2T3fsQrYqf9SyF0QXWHmcGPQ+qiOVjlYSzlWd9dgppB2bFSM1GgKKkt7uzn0BBMV3IbJsUfHG2+BCg==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.yarnpkg.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn@^8.9.0:
  version "8.15.0"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-8.15.0.tgz#a360898bc415edaac46c8241f6383975b930b816"
  integrity sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^1.6.7:
  version "1.11.0"
  resolved "https://registry.yarnpkg.com/axios/-/axios-1.11.0.tgz#c2ec219e35e414c025b2095e8b8280278478fdb6"
  integrity sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.4"
    proxy-from-env "^1.1.0"

babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

backoff@2.5.0:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/backoff/-/backoff-2.5.0.tgz#f616eda9d3e4b66b8ca7fca79f695722c5f8e26f"
  integrity sha512-wC5ihrnUXmR2douXmXLCe5O3zg3GKIyvRi/hi58a/XyRxVI+3/yM0PYueQOZXPXQ9pxBislYkw+sF9b7C/RuMA==
  dependencies:
    precond "0.2"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

birpc@^2.3.0:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/birpc/-/birpc-2.5.0.tgz#3a014e54c17eceba0ce15738d484ea371dbf6527"
  integrity sha512-VSWO/W6nNQdyP520F1mhf+Lc2f8pjGQOtoHHm7Ze8Go1kX7akpVIrtTa0fn+HB0QJEDVacl6aO08YE0PgXfdnQ==

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==

bootstrap@^5.2.1:
  version "5.3.7"
  resolved "https://registry.yarnpkg.com/bootstrap/-/bootstrap-5.3.7.tgz#8640065036124d961d885d80b5945745e1154d90"
  integrity sha512-7KgiD8UHjfcPBHEpDNg+zGz8L3LqR3GVwqZiBRFX04a1BCArZOz1r2kjly2HQ0WokqTO0v1nF+QAt8dsW4lKlw==

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.12.tgz#ab9b454466e5a8cc3a187beaad580412a9c5b843"
  integrity sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/braces/-/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/call-bound/-/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

capacitor-native-settings@^7.0.1:
  version "7.0.2"
  resolved "https://registry.yarnpkg.com/capacitor-native-settings/-/capacitor-native-settings-7.0.2.tgz#1fdab8301ab82a0e60cbf41dd1b65ed758d4a651"
  integrity sha512-s6xsLW0mJ6tywPgGtPXMgfDGTH5UqdSry1ZRlqh9T124IEIuPQyA35zpBaCywfHkmDKEZYdBik3/8Mdncjbniw==

capacitor-plugin-silent-notifications@^6.0.1:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/capacitor-plugin-silent-notifications/-/capacitor-plugin-silent-notifications-6.0.1.tgz#c46b3710f5135bac4ea95cb10f972f948eefaa3d"
  integrity sha512-iMiwPYtdGY6LsC2mRLcnzlx6ZUjX1TskVp3uXrjXKvjIyckB5pu0sVpKnpLOTTZwFPg1ysHtb0FdwKz3gHoCDg==

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^4.0.0:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/chokidar/-/chokidar-4.0.3.tgz#7be37a4c03c9aee1ecfe862a4a23b2c70c205d30"
  integrity sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==
  dependencies:
    readdirp "^4.0.1"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.3:
  version "2.20.3"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

copy-anything@^3.0.2:
  version "3.0.5"
  resolved "https://registry.yarnpkg.com/copy-anything/-/copy-anything-3.0.5.tgz#2d92dce8c498f790fa7ad16b01a1ae5a45b020a0"
  integrity sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==
  dependencies:
    is-what "^4.1.8"

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==

countries-list@^3.1.0:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/countries-list/-/countries-list-3.1.1.tgz#f3a339a06228ff053c0e8e44f17fa5ad4f3cab58"
  integrity sha512-nPklKJ5qtmY5MdBKw1NiBAoyx5Sa7p2yPpljZyQ7gyCN1m+eMFs9I6CT37Mxt8zvR5L3VzD3DJBE4WQzX3WF4A==

cross-spawn@^7.0.2:
  version "7.0.6"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssfilter@0.0.10:
  version "0.0.10"
  resolved "https://registry.yarnpkg.com/cssfilter/-/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

dayjs@^1.11.13:
  version "1.11.13"
  resolved "https://registry.yarnpkg.com/dayjs/-/dayjs-1.11.13.tgz#92430b0139055c3ebb60150aa13e860a4b5a366c"
  integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==

debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.4.1"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==
  dependencies:
    ms "^2.1.3"

debug@~4.3.1, debug@~4.3.2:
  version "4.3.7"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.3.7.tgz#87945b4151a011d76d95a198d7111c865c360a52"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deep-pick-omit@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/deep-pick-omit/-/deep-pick-omit-1.2.1.tgz#c035b27a4c86518b76f60ffa3004e6c61f317032"
  integrity sha512-2J6Kc/m3irCeqVG42T+SaUMesaK7oGWaedGnQQK/+O0gYc+2SP5bKh/KKTE7d7SJ+GCA9UUE1GRzh6oDe0EnGw==

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

defu@^6.1.4:
  version "6.1.4"
  resolved "https://registry.yarnpkg.com/defu/-/defu-6.1.4.tgz#4e0c9cf9ff68fe5f3d7f2765cc1a012dfdcb0479"
  integrity sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

destr@^2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/destr/-/destr-2.0.5.tgz#7d112ff1b925fb8d2079fac5bdb4a90973b51fdb"
  integrity sha512-ugFTXCtDZunbzasqBxrK93Ik/DRYsO6S/fedkWEMKqt04xZ4csmnmwGDBAb07QWNaGMAmnTIemsYZCksjATwsA==

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/detect-libc/-/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/dunder-proto/-/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

emailjs-addressparser@^2.0.1:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/emailjs-addressparser/-/emailjs-addressparser-2.0.3.tgz#b9c1cbaf70da13622075944ce757f3be5451fb99"
  integrity sha512-GjahNdp1fRsWBGxmwC4o7XEoEf7QsdsVrbZFoeulEKNp49NrIUZcZfGMKCPDv5kExiuJLulq/7tJY+ei6BRitA==

emailjs-base64@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/emailjs-base64/-/emailjs-base64-1.1.4.tgz#392fa38cb6aa35dccd3af3637ffc14c1c7ce9612"
  integrity sha512-4h0xp1jgVTnIQBHxSJWXWanNnmuc5o+k4aHEpcLXSToN8asjB5qbXAexs7+PEsUKcEyBteNYsSvXUndYT2CGGA==

emailjs-mime-codec@^2.0.8:
  version "2.0.9"
  resolved "https://registry.yarnpkg.com/emailjs-mime-codec/-/emailjs-mime-codec-2.0.9.tgz#d184451b6f2e55c5868b0f0a82d18fe2b82f0c97"
  integrity sha512-7qJo4pFGcKlWh/kCeNjmcgj34YoJWY0ekZXEHYtluWg4MVBnXqGM4CRMtZQkfYwitOhUgaKN5EQktJddi/YIDQ==
  dependencies:
    emailjs-base64 "^1.1.4"
    ramda "^0.26.1"
    text-encoding "^0.7.0"

emailjs-mime-parser@^2.0.7:
  version "2.0.7"
  resolved "https://registry.yarnpkg.com/emailjs-mime-parser/-/emailjs-mime-parser-2.0.7.tgz#b9641a5564aeef1bd9ff4f7c51ab09b0cd7524d7"
  integrity sha512-rOrRtAzq0OVLrxbTkRLyrtoY/YQldPgIzAk6lcD3LfXR0Gw3+PzsN2rO1QENY+cIQD94vYr2t2Ri0Zxlc9aeew==
  dependencies:
    emailjs-addressparser "^2.0.1"
    emailjs-mime-codec "^2.0.8"
    ramda "^0.26.1"

engine.io-client@~6.6.1:
  version "6.6.3"
  resolved "https://registry.yarnpkg.com/engine.io-client/-/engine.io-client-6.6.3.tgz#815393fa24f30b8e6afa8f77ccca2f28146be6de"
  integrity sha512-T0iLjnyNWahNyv/lcjS2y4oE358tVS/SYQNxYXGAJ9/GLgH4VCvOQ/mhTjqU88mLZCQgiG8RIegFHYCdVC+j5w==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"
    xmlhttprequest-ssl "~2.1.1"

engine.io-parser@~5.2.1:
  version "5.2.3"
  resolved "https://registry.yarnpkg.com/engine.io-parser/-/engine.io-parser-5.2.3.tgz#00dc5b97b1f233a23c9398d0209504cf5f94d92f"
  integrity sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==

entities@^4.5.0:
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/entities/-/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/es-define-property/-/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

esbuild-android-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-android-64/-/esbuild-android-64-0.15.18.tgz#20a7ae1416c8eaade917fb2453c1259302c637a5"
  integrity sha512-wnpt3OXRhcjfIDSZu9bnzT4/TNTDsOUvip0foZOUBG7QbSt//w3QV4FInVJxNhKc/ErhUxc5z4QjHtMi7/TbgA==

esbuild-android-arm64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.18.tgz#9cc0ec60581d6ad267568f29cf4895ffdd9f2f04"
  integrity sha512-G4xu89B8FCzav9XU8EjsXacCKSG2FT7wW9J6hOc18soEHJdtWu03L3TQDGf0geNxfLTtxENKBzMSq9LlbjS8OQ==

esbuild-darwin-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.18.tgz#428e1730ea819d500808f220fbc5207aea6d4410"
  integrity sha512-2WAvs95uPnVJPuYKP0Eqx+Dl/jaYseZEUUT1sjg97TJa4oBtbAKnPnl3b5M9l51/nbx7+QAEtuummJZW0sBEmg==

esbuild-darwin-arm64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.18.tgz#b6dfc7799115a2917f35970bfbc93ae50256b337"
  integrity sha512-tKPSxcTJ5OmNb1btVikATJ8NftlyNlc8BVNtyT/UAr62JFOhwHlnoPrhYWz09akBLHI9nElFVfWSTSRsrZiDUA==

esbuild-freebsd-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.18.tgz#4e190d9c2d1e67164619ae30a438be87d5eedaf2"
  integrity sha512-TT3uBUxkteAjR1QbsmvSsjpKjOX6UkCstr8nMr+q7zi3NuZ1oIpa8U41Y8I8dJH2fJgdC3Dj3CXO5biLQpfdZA==

esbuild-freebsd-arm64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.18.tgz#18a4c0344ee23bd5a6d06d18c76e2fd6d3f91635"
  integrity sha512-R/oVr+X3Tkh+S0+tL41wRMbdWtpWB8hEAMsOXDumSSa6qJR89U0S/PpLXrGF7Wk/JykfpWNokERUpCeHDl47wA==

esbuild-linux-32@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-32/-/esbuild-linux-32-0.15.18.tgz#9a329731ee079b12262b793fb84eea762e82e0ce"
  integrity sha512-lphF3HiCSYtaa9p1DtXndiQEeQDKPl9eN/XNoBf2amEghugNuqXNZA/ZovthNE2aa4EN43WroO0B85xVSjYkbg==

esbuild-linux-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-64/-/esbuild-linux-64-0.15.18.tgz#532738075397b994467b514e524aeb520c191b6c"
  integrity sha512-hNSeP97IviD7oxLKFuii5sDPJ+QHeiFTFLoLm7NZQligur8poNOWGIgpQ7Qf8Balb69hptMZzyOBIPtY09GZYw==

esbuild-linux-arm64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.18.tgz#5372e7993ac2da8f06b2ba313710d722b7a86e5d"
  integrity sha512-54qr8kg/6ilcxd+0V3h9rjT4qmjc0CccMVWrjOEM/pEcUzt8X62HfBSeZfT2ECpM7104mk4yfQXkosY8Quptug==

esbuild-linux-arm@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.18.tgz#e734aaf259a2e3d109d4886c9e81ec0f2fd9a9cc"
  integrity sha512-UH779gstRblS4aoS2qpMl3wjg7U0j+ygu3GjIeTonCcN79ZvpPee12Qun3vcdxX+37O5LFxz39XeW2I9bybMVA==

esbuild-linux-mips64le@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.18.tgz#c0487c14a9371a84eb08fab0e1d7b045a77105eb"
  integrity sha512-Mk6Ppwzzz3YbMl/ZZL2P0q1tnYqh/trYZ1VfNP47C31yT0K8t9s7Z077QrDA/guU60tGNp2GOwCQnp+DYv7bxQ==

esbuild-linux-ppc64le@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.18.tgz#af048ad94eed0ce32f6d5a873f7abe9115012507"
  integrity sha512-b0XkN4pL9WUulPTa/VKHx2wLCgvIAbgwABGnKMY19WhKZPT+8BxhZdqz6EgkqCLld7X5qiCY2F/bfpUUlnFZ9w==

esbuild-linux-riscv64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.18.tgz#423ed4e5927bd77f842bd566972178f424d455e6"
  integrity sha512-ba2COaoF5wL6VLZWn04k+ACZjZ6NYniMSQStodFKH/Pu6RxzQqzsmjR1t9QC89VYJxBeyVPTaHuBMCejl3O/xg==

esbuild-linux-s390x@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.18.tgz#21d21eaa962a183bfb76312e5a01cc5ae48ce8eb"
  integrity sha512-VbpGuXEl5FCs1wDVp93O8UIzl3ZrglgnSQ+Hu79g7hZu6te6/YHgVJxCM2SqfIila0J3k0csfnf8VD2W7u2kzQ==

esbuild-netbsd-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.18.tgz#ae75682f60d08560b1fe9482bfe0173e5110b998"
  integrity sha512-98ukeCdvdX7wr1vUYQzKo4kQ0N2p27H7I11maINv73fVEXt2kyh4K4m9f35U1K43Xc2QGXlzAw0K9yoU7JUjOg==

esbuild-openbsd-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.18.tgz#79591a90aa3b03e4863f93beec0d2bab2853d0a8"
  integrity sha512-yK5NCcH31Uae076AyQAXeJzt/vxIo9+omZRKj1pauhk3ITuADzuOx5N2fdHrAKPxN+zH3w96uFKlY7yIn490xQ==

esbuild-sunos-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.18.tgz#fd528aa5da5374b7e1e93d36ef9b07c3dfed2971"
  integrity sha512-On22LLFlBeLNj/YF3FT+cXcyKPEI263nflYlAhz5crxtp3yRG1Ugfr7ITyxmCmjm4vbN/dGrb/B7w7U8yJR9yw==

esbuild-windows-32@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-windows-32/-/esbuild-windows-32-0.15.18.tgz#0e92b66ecdf5435a76813c4bc5ccda0696f4efc3"
  integrity sha512-o+eyLu2MjVny/nt+E0uPnBxYuJHBvho8vWsC2lV61A7wwTWC3jkN2w36jtA+yv1UgYkHRihPuQsL23hsCYGcOQ==

esbuild-windows-64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-windows-64/-/esbuild-windows-64-0.15.18.tgz#0fc761d785414284fc408e7914226d33f82420d0"
  integrity sha512-qinug1iTTaIIrCorAUjR0fcBk24fjzEedFYhhispP8Oc7SFvs+XeW3YpAKiKp8dRpizl4YYAhxMjlftAMJiaUw==

esbuild-windows-arm64@0.15.18:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.18.tgz#5b5bdc56d341d0922ee94965c89ee120a6a86eb7"
  integrity sha512-q9bsYzegpZcLziq0zgUi5KqGVtfhjxGbnksaBFYmWLxeV/S1fK4OLdq2DFYnXcLMjlZw2L0jLsk1eGoB522WXQ==

esbuild@^0.15.9:
  version "0.15.18"
  resolved "https://registry.yarnpkg.com/esbuild/-/esbuild-0.15.18.tgz#ea894adaf3fbc036d32320a00d4d6e4978a2f36d"
  integrity sha512-x/R72SmW3sSFRm5zrrIjAhCeQSAWoni3CmHEqfQrZIQTM3lVCdehdwuIqaOtfC2slvpdlLa62GYoN8SxT23m6Q==
  optionalDependencies:
    "@esbuild/android-arm" "0.15.18"
    "@esbuild/linux-loong64" "0.15.18"
    esbuild-android-64 "0.15.18"
    esbuild-android-arm64 "0.15.18"
    esbuild-darwin-64 "0.15.18"
    esbuild-darwin-arm64 "0.15.18"
    esbuild-freebsd-64 "0.15.18"
    esbuild-freebsd-arm64 "0.15.18"
    esbuild-linux-32 "0.15.18"
    esbuild-linux-64 "0.15.18"
    esbuild-linux-arm "0.15.18"
    esbuild-linux-arm64 "0.15.18"
    esbuild-linux-mips64le "0.15.18"
    esbuild-linux-ppc64le "0.15.18"
    esbuild-linux-riscv64 "0.15.18"
    esbuild-linux-s390x "0.15.18"
    esbuild-netbsd-64 "0.15.18"
    esbuild-openbsd-64 "0.15.18"
    esbuild-sunos-64 "0.15.18"
    esbuild-windows-32 "0.15.18"
    esbuild-windows-64 "0.15.18"
    esbuild-windows-arm64 "0.15.18"

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-prettier@^8.3.0:
  version "8.10.2"
  resolved "https://registry.yarnpkg.com/eslint-config-prettier/-/eslint-config-prettier-8.10.2.tgz#0642e53625ebc62c31c24726b0f050df6bd97a2e"
  integrity sha512-/IGJ6+Dka158JnP5n5YFMOszjDWrXggGz1LaK/guZq9vZTmniaKlHcsscvkAhn9y4U+BU3JuUdYvtAMcv30y4A==

eslint-plugin-prettier@^4.0.0:
  version "4.2.5"
  resolved "https://registry.yarnpkg.com/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.5.tgz#91ca3f2f01a84f1272cce04e9717550494c0fe06"
  integrity sha512-9Ni+xgemM2IWLq6aXEpP2+V/V30GeA/46Ar629vcMqVPodFFWC9skHu/D1phvuqtS8bJCFnNf01/qcmqYEwNfg==
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-vue@^9.0.0:
  version "9.33.0"
  resolved "https://registry.yarnpkg.com/eslint-plugin-vue/-/eslint-plugin-vue-9.33.0.tgz#de33eba8f78e1d172c59c8ec7fbfd60c6ca35c39"
  integrity sha512-174lJKuNsuDIlLpjeXc5E2Tss8P44uIimAfGD0b90k0NoirJqpG7stLuU9Vp/9ioTOrQdWVREc4mRd1BD+CvGw==
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    globals "^13.24.0"
    natural-compare "^1.4.0"
    nth-check "^2.1.1"
    postcss-selector-parser "^6.0.15"
    semver "^7.6.3"
    vue-eslint-parser "^9.4.3"
    xml-name-validator "^4.0.0"

eslint-scope@^7.1.1, eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry.yarnpkg.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@^8.5.0:
  version "8.57.1"
  resolved "https://registry.yarnpkg.com/eslint/-/eslint-8.57.1.tgz#7df109654aba7e3bbe5c8eae533c5e461d3c6ca9"
  integrity sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.3.1, espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://registry.yarnpkg.com/espree/-/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.0, esquery@^1.4.2:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/esquery/-/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/estree-walker/-/estree-walker-0.6.1.tgz#53049143f40c6eb918b23671d1fe3219f3a1b362"
  integrity sha512-SqmZANLWS0mnatqbSfRP5g8OXZC12Fgg1IwNtLsyHDzJizORW4khDfjPqJZsemPWBB2uqykUah5YpQ6epsqC/w==

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/estree-walker/-/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/fast-diff/-/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==

fast-equals@^5.2.2:
  version "5.2.2"
  resolved "https://registry.yarnpkg.com/fast-equals/-/fast-equals-5.2.2.tgz#885d7bfb079fac0ce0e8450374bce29e9b742484"
  integrity sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry.yarnpkg.com/fastq/-/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://registry.yarnpkg.com/flatted/-/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==

follow-redirects@^1.15.6:
  version "1.15.11"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.11.tgz#777d73d72a92f8ec4d2e410eb47352a56b8e8340"
  integrity sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==

for-each@^0.3.5:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/for-each/-/for-each-0.3.5.tgz#d650688027826920feeb0af747ee7b9421a41d47"
  integrity sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==
  dependencies:
    is-callable "^1.2.7"

form-data@^4.0.4:
  version "4.0.4"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-4.0.4.tgz#784cdcce0669a9d68e94d11ac4eea98088edd2c4"
  integrity sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-intrinsic@^1.2.4, get-intrinsic@^1.2.6, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/get-proto/-/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.19.0, globals@^13.24.0:
  version "13.24.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/gopd/-/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/graphemer/-/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hookable@^5.5.3:
  version "5.5.3"
  resolved "https://registry.yarnpkg.com/hookable/-/hookable-5.5.3.tgz#6cfc358984a1ef991e2518cb9ed4a778bbd3215d"
  integrity sha512-Yc+BQe8SvoXH1643Qez1zqLRmbA5rCL+sSmk6TVos0LWVfNIB7PGncdlId77WzLGSIB5KaWgTaNTs2lNVEI6VQ==

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://registry.yarnpkg.com/ignore/-/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

immutable@^5.0.2:
  version "5.1.3"
  resolved "https://registry.yarnpkg.com/immutable/-/immutable-5.1.3.tgz#e6486694c8b76c37c063cca92399fa64098634d4"
  integrity sha512-+chQdDfvscSF1SJqv2gn4SRO2ZyS3xL3r7IW/wWEEzrzLisnOlKiQu5ytC/BVNcS15C39WT2Hg/bjKjDMcu+zg==

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "https://registry.yarnpkg.com/import-fresh/-/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ionicons@^8.0.13:
  version "8.0.13"
  resolved "https://registry.yarnpkg.com/ionicons/-/ionicons-8.0.13.tgz#afaea839809b819cf073325f54d73b1ba99e59fc"
  integrity sha512-2QQVyG2P4wszne79jemMjWYLp0DBbDhr4/yFroPCxvPP1wtMxgdIV3l5n+XZ5E9mgoXU79w7yTWpm2XzJsISxQ==
  dependencies:
    "@stencil/core" "^4.35.3"

is-arguments@^1.0.4:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.yarnpkg.com/is-core-module/-/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-generator-function/-/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.3:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-reference@^1.1.2, is-reference@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/is-reference/-/is-reference-1.2.1.tgz#8b2dac0b371f4bc994fdeaba9eb542d03002d0b7"
  integrity sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ==
  dependencies:
    "@types/estree" "*"

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/is-regex/-/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-typed-array@^1.1.3:
  version "1.1.15"
  resolved "https://registry.yarnpkg.com/is-typed-array/-/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
  dependencies:
    which-typed-array "^1.1.16"

is-what@^4.1.8:
  version "4.1.16"
  resolved "https://registry.yarnpkg.com/is-what/-/is-what-4.1.16.tgz#1ad860a19da8b4895ad5495da3182ce2acdd7a6f"
  integrity sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

iso-country-currency@^0.7.2:
  version "0.7.2"
  resolved "https://registry.yarnpkg.com/iso-country-currency/-/iso-country-currency-0.7.2.tgz#53f28cded446391cef4d97ce340bec61318bc880"
  integrity sha512-RcFyqdvt1nbPPrUfAs2cuWnjynZYHU671r9MlcH7zn9Zkyif3tR9LJWVJmpFeSMDECDeycDUNiy9Mur+B21Sqw==

"js-marker-clusterer@github:googlemaps/markerclusterer":
  version "1.0.0"
  resolved "https://codeload.github.com/googlemaps/markerclusterer/tar.gz/8c2be07696e0c8789a4e314e12fc698622bf8323"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/json-buffer/-/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

kdbush@^4.0.2:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/kdbush/-/kdbush-4.0.2.tgz#2f7b7246328b4657dd122b6c7f025fbc2c868e39"
  integrity sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.yarnpkg.com/keyv/-/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/levn/-/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash-es/-/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.yarnpkg.com/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

loglevel@1.6.7:
  version "1.6.7"
  resolved "https://registry.yarnpkg.com/loglevel/-/loglevel-1.6.7.tgz#b3e034233188c68b889f5b862415306f565e2c56"
  integrity sha512-cY2eLFrQSAfVPhCgH1s7JI73tMbg9YC3v3+ZHVW67sBS7UxWzNEk/ZBbSfLykBWHp33dqqtOv82gjhKEi81T/A==

magic-string@^0.25.2, magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://registry.yarnpkg.com/magic-string/-/magic-string-0.25.9.tgz#de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c"
  integrity sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.30.17:
  version "0.30.17"
  resolved "https://registry.yarnpkg.com/magic-string/-/magic-string-0.30.17.tgz#450a449673d2460e5bbcfba9a61916a1714c7453"
  integrity sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

maska@^3.1.1:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/maska/-/maska-3.2.0.tgz#0287c8e9cb137d3e08bc65a28547ebc225fbab11"
  integrity sha512-zSmSgs5/q9vMSmrdZT3rKOv9uLznNWR/niuuAdBZDTvB3SMKOX9vhMtDijFyExz+B4UClu2rvksylUh/ea1bLA==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

micromatch@^4.0.5:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

mitt@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/mitt/-/mitt-3.0.1.tgz#ea36cf0cc30403601ae074c8f77b7092cdab36d1"
  integrity sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==

moment@^2.30.1:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://registry.yarnpkg.com/nanoid/-/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "https://registry.yarnpkg.com/node-addon-api/-/node-addon-api-7.1.1.tgz#1aba6693b0f255258a049d621329329322aad558"
  integrity sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==

nth-check@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/nth-check/-/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/perfect-debounce/-/perfect-debounce-1.0.0.tgz#9c2e8bc30b169cc984a58b7d5b28049839591d2a"
  integrity sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/picocolors/-/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pinia-plugin-persistedstate@^4.3.0:
  version "4.4.1"
  resolved "https://registry.yarnpkg.com/pinia-plugin-persistedstate/-/pinia-plugin-persistedstate-4.4.1.tgz#2cc60432d8b5b56c49cdd1c604a709f2e2864d60"
  integrity sha512-lmuMPpXla2zJKjxEq34e1E9P9jxkWEhcVwwioCCE0izG45kkTOvQfCzvwhW3i38cvnaWC7T1eRdkd15Re59ldw==
  dependencies:
    deep-pick-omit "^1.2.1"
    defu "^6.1.4"
    destr "^2.0.5"

pinia@^3.0.2:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/pinia/-/pinia-3.0.3.tgz#f412019bdeb2f45e85927b432803190343e12d89"
  integrity sha512-ttXO/InUULUXkMHpTdp9Fj4hLpD/2AoJdmAbAeW2yu1iy1k+pkFekQXw5VpC0/5p51IOR/jDaDRfRWRnMMsGOA==
  dependencies:
    "@vue/devtools-api" "^7.7.2"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz#93e3582bc0e5426586d9d07b79ee40fc841de4ae"
  integrity sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==

postcss-selector-parser@^6.0.15:
  version "6.1.2"
  resolved "https://registry.yarnpkg.com/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz#27ecb41fb0e3b6ba7a1ec84fff347f734c7929de"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss@^8.4.18, postcss@^8.5.6:
  version "8.5.6"
  resolved "https://registry.yarnpkg.com/postcss/-/postcss-8.5.6.tgz#2825006615a619b4f62a9e7426cc120b349a8f3c"
  integrity sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

precond@0.2:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/precond/-/precond-0.2.3.tgz#aa9591bcaa24923f1e0f4849d240f47efc1075ac"
  integrity sha512-QCYG84SgGyGzqJ/vlMsxeXd/pgL/I94ixdNFyh1PusWmTCyVfPJjZ1K1jvHtsbfnXQs2TSkEP2fR7QiMZAnKFQ==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.5.1:
  version "2.8.8"
  resolved "https://registry.yarnpkg.com/prettier/-/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

ramda@^0.26.1:
  version "0.26.1"
  resolved "https://registry.yarnpkg.com/ramda/-/ramda-0.26.1.tgz#8d41351eb8111c55353617fc3bbffad8e4d35d06"
  integrity sha512-hLWjpy7EnsDBb0p+Z3B7rPi3GDeRG5ZtiI33kJhTt+ORCd38AbAIjB/9zRIUoeTbE/AVX5ZkU7m6bznsvrf8eQ==

readdirp@^4.0.1:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/readdirp/-/readdirp-4.1.2.tgz#eb85801435fbf2a7ee58f19e0921b068fc69948d"
  integrity sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.11.0, resolve@^1.22.1:
  version "1.22.10"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/reusify/-/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==

rfdc@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/rfdc/-/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup-plugin-commonjs@^10.1.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/rollup-plugin-commonjs/-/rollup-plugin-commonjs-10.1.0.tgz#417af3b54503878e084d127adf4d1caf8beb86fb"
  integrity sha512-jlXbjZSQg8EIeAAvepNwhJj++qJWNJw1Cl0YnOqKtP5Djx+fFGkp3WRh+W0ASCaFG5w1jhmzDxgu3SJuVxPF4Q==
  dependencies:
    estree-walker "^0.6.1"
    is-reference "^1.1.2"
    magic-string "^0.25.2"
    resolve "^1.11.0"
    rollup-pluginutils "^2.8.1"

rollup-plugin-external-globals@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/rollup-plugin-external-globals/-/rollup-plugin-external-globals-0.6.1.tgz#861c260b5727144e0fd1b424b103f9f0282fc365"
  integrity sha512-mlp3KNa5sE4Sp9UUR2rjBrxjG79OyZAh/QC18RHIjM+iYkbBwNXSo8DHRMZWtzJTrH8GxQ+SJvCTN3i14uMXIA==
  dependencies:
    "@rollup/pluginutils" "^4.0.0"
    estree-walker "^2.0.1"
    is-reference "^1.2.1"
    magic-string "^0.25.7"

rollup-pluginutils@^2.8.1:
  version "2.8.2"
  resolved "https://registry.yarnpkg.com/rollup-pluginutils/-/rollup-pluginutils-2.8.2.tgz#72f2af0748b592364dbd3389e600e5a9444a351e"
  integrity sha512-EEp9NhnUkwY8aif6bxgovPHMoMoNr2FulJziTndpt5H9RdwC47GSGuII9XxpSdzVGM0GWrNPHV6ie1LTNJPaLQ==
  dependencies:
    estree-walker "^0.6.1"

rollup@^2.79.1:
  version "2.79.2"
  resolved "https://registry.yarnpkg.com/rollup/-/rollup-2.79.2.tgz#f150e4a5db4b121a21a747d762f701e5e9f49090"
  integrity sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ==
  optionalDependencies:
    fsevents "~2.3.2"

rtcpeerconnection-shim@1.2.8:
  version "1.2.8"
  resolved "https://registry.yarnpkg.com/rtcpeerconnection-shim/-/rtcpeerconnection-shim-1.2.8.tgz#1d579d0f1d7aa8281c78d4ec9251017b04646e3a"
  integrity sha512-5Sx90FGru1sQw9aGOM+kHU4i6mbP8eJPgxliu2X3Syhg8qgDybx8dpDTxUwfJvPnubXFnZeRNl59DWr4AttJKQ==
  dependencies:
    sdp "^2.6.0"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/safe-regex-test/-/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

sass@^1.53.0:
  version "1.89.2"
  resolved "https://registry.yarnpkg.com/sass/-/sass-1.89.2.tgz#a771716aeae774e2b529f72c0ff2dfd46c9de10e"
  integrity sha512-xCmtksBKd/jdJ9Bt9p7nPKiuqrlBMBuuGkQlkhZjjQk3Ty48lv93k5Dq6OPkKt4XwxDJ7tvlfrTa1MPA9bf+QA==
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

sdp@^2.6.0:
  version "2.12.0"
  resolved "https://registry.yarnpkg.com/sdp/-/sdp-2.12.0.tgz#338a106af7560c86e4523f858349680350d53b22"
  integrity sha512-jhXqQAQVM+8Xj5EjJGVweuEzgtGWb3tmEEpl3CLP3cStInSbVHSg0QWOGQzNq8pSID4JkpeV2mPqlMDLrm0/Vw==

semver@^7.3.6, semver@^7.6.3:
  version "7.7.2"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

socket.io-client@^4.8.1:
  version "4.8.1"
  resolved "https://registry.yarnpkg.com/socket.io-client/-/socket.io-client-4.8.1.tgz#1941eca135a5490b94281d0323fe2a35f6f291cb"
  integrity sha512-hJVXfu3E28NmzGk8o1sHhN3om52tRvwYeidbj7xKy2eIIse5IoKX3USlS6Tqt3BHAtflLIkCQBkzVrEEfWUyYQ==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.2"
    engine.io-client "~6.6.1"
    socket.io-parser "~4.2.4"

socket.io-parser@~4.2.4:
  version "4.2.4"
  resolved "https://registry.yarnpkg.com/socket.io-parser/-/socket.io-parser-4.2.4.tgz#c806966cf7270601e47469ddeec30fbdfda44c83"
  integrity sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/source-map-js/-/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://registry.yarnpkg.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

speakingurl@^14.0.1:
  version "14.0.1"
  resolved "https://registry.yarnpkg.com/speakingurl/-/speakingurl-14.0.1.tgz#f37ec8ddc4ab98e9600c1c9ec324a8c48d772a53"
  integrity sha512-1POYv7uv2gXoyGFpBCmpDVSNV74IfsWlDW216UPjbWufNf+bSU6GdbDsxdcxtfwb4xlI3yxzOTKClUosxARYrQ==

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

supercluster@^8.0.1:
  version "8.0.1"
  resolved "https://registry.yarnpkg.com/supercluster/-/supercluster-8.0.1.tgz#9946ba123538e9e9ab15de472531f604e7372df5"
  integrity sha512-IiOea5kJ9iqzD2t7QJq/cREyLHTtSmUT6gQsweojg9WH2sYJqZK9SswTu6jrscO6D1G5v5vYZ9ru/eq85lXeZQ==
  dependencies:
    kdbush "^4.0.2"

superjson@^2.2.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/superjson/-/superjson-2.2.2.tgz#9d52bf0bf6b5751a3c3472f1292e714782ba3173"
  integrity sha512-5JRxVqC8I8NuOUjzBbvVJAKNM8qoVuH0O77h4WInc/qC2q5IreqKxYwgkga3PfA22OayK2ikceb/B26dztPl+Q==
  dependencies:
    copy-anything "^3.0.2"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

swiper@^11.2.8:
  version "11.2.10"
  resolved "https://registry.yarnpkg.com/swiper/-/swiper-11.2.10.tgz#ed0b17286b56f7fe8d4b46ed61e6e0bd8daaccad"
  integrity sha512-RMeVUUjTQH+6N3ckimK93oxz6Sn5la4aDlgPzB+rBrG/smPdCTicXyhxa+woIpopz+jewEloiEE3lKo1h9w2YQ==

text-encoding@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/text-encoding/-/text-encoding-0.7.0.tgz#f895e836e45990624086601798ea98e8f36ee643"
  integrity sha512-oJQ3f1hrOnbRLOcwKz0Liq2IcrvDeZRHXhd9RgLrsT+DjWY/nty1Hi7v3dtkaEYbPYe0mUoOfzRrMwfXXwgPUA==

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

tslib@^2.1.0:
  version "2.8.1"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

twilio-client@^1.15.1:
  version "1.15.1"
  resolved "https://registry.yarnpkg.com/twilio-client/-/twilio-client-1.15.1.tgz#c884f91dc5b18bb93eb521bffd5fa5e778b10675"
  integrity sha512-O2Cdq4THosTPLMIfFK0/xcS8SCCLUiv1hDB4i4sAisqtfHEVLXSuNoL332rPkDPC3jzgm3C5zzbEz/XyEXO+NQ==
  dependencies:
    "@twilio/audioplayer" "1.0.6"
    "@twilio/voice-errors" "1.0.1"
    backoff "2.5.0"
    loglevel "1.6.7"
    rtcpeerconnection-shim "1.2.8"
    ws "7.4.6"
    xmlhttprequest "1.8.0"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

typescript@4.9.5:
  version "4.9.5"
  resolved "https://registry.yarnpkg.com/typescript/-/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.yarnpkg.com/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util@^0.12.5:
  version "0.12.5"
  resolved "https://registry.yarnpkg.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

vite-plugin-inject-externals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/vite-plugin-inject-externals/-/vite-plugin-inject-externals-1.1.1.tgz#276c0f970dc3388bc80cc78fd36f4039ab6b20e9"
  integrity sha512-Z98kggrOZevR0/E69A4uTt0wo7rrvhbqMYJ1tGNhtM7R3OrIaQz5nntfCGvZUQEl4o82zZHJqIGg4/fKqp2J+w==
  dependencies:
    rollup-plugin-external-globals "^0.6.1"

vite@^3.0.2:
  version "3.2.11"
  resolved "https://registry.yarnpkg.com/vite/-/vite-3.2.11.tgz#8d1c8e05ef2f24b04c8693f56d3e01fe8835e6d7"
  integrity sha512-K/jGKL/PgbIgKCiJo5QbASQhFiV02X9Jh+Qq0AKCRCRKZtOTVi4t6wh75FDpGf2N9rYOnzH87OEFQNaFy6pdxQ==
  dependencies:
    esbuild "^0.15.9"
    postcss "^8.4.18"
    resolve "^1.22.1"
    rollup "^2.79.1"
  optionalDependencies:
    fsevents "~2.3.2"

vue-eslint-parser@^9.4.3:
  version "9.4.3"
  resolved "https://registry.yarnpkg.com/vue-eslint-parser/-/vue-eslint-parser-9.4.3.tgz#9b04b22c71401f1e8bca9be7c3e3416a4bde76a8"
  integrity sha512-2rYRLWlIpaiN8xbPiDyXZXRgLGOtWxERV7ND5fFAv5qo1D2N9Fu9MNajBNc6o13lZ+24DAWCkQCvj4klgmcITg==
  dependencies:
    debug "^4.3.4"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.6"

vue-persist-state@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/vue-persist-state/-/vue-persist-state-2.0.0.tgz#bfb2bf6ab03d6d692676d4a6c2c2731c5732392d"
  integrity sha512-ZawaAfwgx19w4lZa/SsroZFoIXVPMingCk69m8kv1xFBDgAh+c8QGB2U+HhWP6aOFFcHJuyEMuvjAAHGtxEBdg==

vue-router@4.4.0:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/vue-router/-/vue-router-4.4.0.tgz#128e3fc0c84421035a9bd26027245e6bd68f69ab"
  integrity sha512-HB+t2p611aIZraV2aPSRNXf0Z/oLZFrlygJm+sZbdJaW6lcFqEDQwnzUBXn+DApw+/QzDU/I9TeWx9izEjTmsA==
  dependencies:
    "@vue/devtools-api" "^6.5.1"

vue@^3.3.10:
  version "3.5.18"
  resolved "https://registry.yarnpkg.com/vue/-/vue-3.5.18.tgz#3d622425ad1391a2b0138323211ec784f4415686"
  integrity sha512-7W4Y4ZbMiQ3SEo+m9lnoNpV9xG7QVMLa+/0RFwwiAVkeYoyGXqWE85jabU4pllJNUzqfLShJ5YLptewhCWUgNA==
  dependencies:
    "@vue/compiler-dom" "3.5.18"
    "@vue/compiler-sfc" "3.5.18"
    "@vue/runtime-dom" "3.5.18"
    "@vue/server-renderer" "3.5.18"
    "@vue/shared" "3.5.18"

which-typed-array@^1.1.16, which-typed-array@^1.1.2:
  version "1.1.19"
  resolved "https://registry.yarnpkg.com/which-typed-array/-/which-typed-array-1.1.19.tgz#df03842e870b6b88e117524a4b364b6fc689f956"
  integrity sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.yarnpkg.com/word-wrap/-/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@7.4.6:
  version "7.4.6"
  resolved "https://registry.yarnpkg.com/ws/-/ws-7.4.6.tgz#5654ca8ecdeee47c33a9a4bf6d28e2be2980377c"
  integrity sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==

ws@~8.17.1:
  version "8.17.1"
  resolved "https://registry.yarnpkg.com/ws/-/ws-8.17.1.tgz#9293da530bb548febc95371d90f9c878727d919b"
  integrity sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz#79a006e2e63149a8600f15430f0a4725d1524835"
  integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==

xmlhttprequest-ssl@~2.1.1:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.1.2.tgz#e9e8023b3f29ef34b97a859f584c5e6c61418e23"
  integrity sha512-TEU+nJVUUnA4CYJFLvK5X9AOeH4KvDvhIfm0vV1GaQRtchnG0hgK5p8hw/xjv8cunWYCsiPCSDzObPyhEwq3KQ==

xmlhttprequest@1.8.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz#67fe075c5c24fef39f9d65f5f7b7fe75171968fc"
  integrity sha512-58Im/U0mlVBLM38NdZjHyhuMtCqa61469k2YP/AaPbvCoV9aQGUpbJBj1QRm2ytRiVQBD/fsw7L2bJGDVQswBA==

xss@^1.0.15:
  version "1.0.15"
  resolved "https://registry.yarnpkg.com/xss/-/xss-1.0.15.tgz#96a0e13886f0661063028b410ed1b18670f4e59a"
  integrity sha512-FVdlVVC67WOIPvfOwhoMETV72f6GbW7aOabBC3WxN/oUdoEMDyLz4OgRv5/gck2ZeNqEQu+Tb0kloovXOfpYVg==
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
