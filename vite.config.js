import { fileURLToPath, URL } from "url";
import path from "path";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import injectExternalsD from "vite-plugin-inject-externals";
import CDNSettings from "@incomnetworking/pagebuild-cdn-settings";
const injectExternals = injectExternalsD.default;

function camelize(str) {
  let arr = str.split("-");
  let capital = arr.map(
    (item) => item.charAt(0).toUpperCase() + item.slice(1).toLowerCase()
  );
  return capital.join("");
}

function removeScope(str) {
  let arr = str.split("/");
  if (arr.length > 1) {
    return arr[1];
  }
  return str;
}

let packageName = removeScope(process.env.npm_package_name);
let packageExportName = camelize(packageName);

// https://vitejs.dev/config/
export default function (build) {
  if (build.mode == "development") {
    return defineConfig({
      define: {
        "process.env": {
          NODE_ENV: JSON.stringify("development"),
        },
      },
      optimizeDeps: {
        force: true,
        include: [
          "@incomnetworking/pagebuilder-ckeditor",
          "@incomnetworking/vue-component-ckeditor",
          "@incomnetworking/pagebuilder-ckeditor",
          "@incomnetworking/ckeditor5-vue",
          "@incomnetworking/vue-component-multilang-editor",
          "lodash-es",
          "dayjs",
          "@incomnetworking/app-agent-profile-store",
          "moment",
          "moment/moment",
          "twilio-client",
          "xss",
        ],
      },
      plugins: [vue(), injectExternals(CDNSettings)],
      resolve: {
        alias: {
          router: path.resolve(__dirname, "src/router.js"),
          "@": fileURLToPath(new URL("./src", import.meta.url)),
          "#": fileURLToPath(new URL("./node_modules", import.meta.url)),
        },
      },
    });
  }

  return defineConfig({
    plugins: [vue(), injectExternals(CDNSettings)],
    optimizeDeps: {
      force: true,
      include: [
        "@incomnetworking/pagebuilder-ckeditor",
        "@incomnetworking/vue-component-ckeditor",
        "@incomnetworking/pagebuilder-ckeditor",
        "@incomnetworking/ckeditor5-vue",
        "@incomnetworking/vue-component-multilang-editor",
      ],
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
        "#": fileURLToPath(new URL("./node_modules", import.meta.url)),
      },
    },
    build: {
      lib: {
        entry: "./src/index.ts",
        name: packageExportName,
        // the proper extensions will be added
        fileName: packageName,
        formats: ["umd"],
      },
      rollupOptions: {
        // make sure to externalize deps that shouldn't be bundled
        // into your library
        external: [
          "vue",
          "@ionic/vue",
          "@capacitor/core",
          "ionicons/icons",
          "@capacitor/preferences",
          "pinia",
          "router",
          "@incomnetworking/pagebuilder-ckeditor",
          "@incomnetworking/agent-app-helper-functions",
          "@incomnetworking/app-agent-auth-store",
          "@incomnetworking/app-agent-user-config-store",
          "@incomnetworking/app-agent-message-store",
          "@incomnetworking/agent-app-tabs-controler-store",
          "@incomnetworking/app-agent-profile-store",
          "@incomnetworking/app-agent-lead-store",
          "@incomnetworking/app-agent-push-notification-store",
          "@incomnetworking/app-agent-global-preferences-store",
          "@incomnetworking/vue-components-app-icons",
          "@incomnetworking/vue-component-app-terms-and-conditions",
          "@incomnetworking/vue-component-app-action-sheet",
          "@incomnetworking/app-agent-lead-profile-header-vue-components",
          "@incomnetworking/app-agent-types",
          "@incomnetworking/app-agent-service-leads",
          "@incomnetworking/app-agent-multi-select-typehead-vue-component",
          "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component",
          "@incomnetworking/app-agent-mass-message-action-sheet-vue-component",
          "@incomnetworking/app-agent-leads-list-vue-component",
          "@incomnetworking/app-agent-mass-message-modal-vue-component",
          "@incomnetworking/app-agent-view-leads-page",
        ],
        output: {
          // Provide global variables to use in the UMD build
          // for externalized deps
          globals: {
            vue: "Vue",
            "@incomnetworking/pagebuilder-ckeditor": "ClassicEditor",
          },
        },
      },
    },
  });
}
