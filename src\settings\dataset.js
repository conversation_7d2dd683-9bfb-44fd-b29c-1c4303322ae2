export default {
  accessToken: {
    type: "object",
    default: {},
  },
  search: {
    type: "object",
    default: {},
  },
  edit: {
    type: "object",
    default: {},
  },
  domain: {
    type: "object",
    default: {},
  },
  page: {
    type: "object",
    default: {},
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  widgets: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  sections: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  templateWidgets: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  templateSections: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  agents: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  offices: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  items: {
    type: "array",
    default: [],
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  agent: {
    type: "object",
    default: {},
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  office: {
    type: "object",
    default: {},
    provider: function () {
      // first parameter is app, second is callback
    },
  },
  item: {
    type: "object",
    default: {},
    provider: function () {
      // first parameter is app, second is callback
    },
  },
};
