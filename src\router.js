import { createRouter, createWebHistory } from "@ionic/vue-router";

import LoginDetailsPage from "@incomnetworking/app-agent-view-login-page";
import LeadCategoriesPage from "@incomnetworking/app-agent-view-lead-categories-page";
import AddLeadCategoryPage from "@incomnetworking/app-agent-view-add-lead-category-page";
import AddLeadPage from "@incomnetworking/app-agent-view-add-lead-page";
import TabsPage from "@incomnetworking/app-agent-view-tabs-page";
import NotesPage from "@incomnetworking/app-agent-view-notes-page";
import LeadSearches from "@incomnetworking/app-agent-view-searches-page";
import LeadPage from "@incomnetworking/app-agent-view-manage-lead-page";
import ActivityLog from "@incomnetworking/app-agent-view-activity-log-page";
import TasksPage from "@incomnetworking/app-agent-view-tasks-page";
import MessagesPage from "@incomnetworking/app-agent-view-messages-page";
import TaskView from "@incomnetworking/app-agent-view-manage-task-view";
import TaskPage from "@incomnetworking/app-agent-view-manage-task-page";
const routes = [
  {
    path: "/login",
    name: "login",
    component: LoginDetailsPage,
  },
  {
    path: "/",
    redirect: { name: "leads", params: {} },
  },
  {
    path: "/tabs",
    component: TabsPage,
    children: [
      {
        path: "/tabs/leads",
        name: "leads",
        component: () => import("./component-view.vue"),
        children: [
          {
            path: "",
            name: "lead-landing",
            component: () => import("./component-view-landing.vue"),
          },
          {
            path: ":id/:viewMode?/:backToPrevPage?/:forceEdit?/:openSubmissions?",
            name: "lead",
            component: LeadPage,
          },
          {
            path: ":id/:viewMode?/:backToPrevPage?/:forceEdit?/:openNotifications?",
            name: "lead",
            component: LeadPage,
          },
          {
            path: ":leadId/lead-activity",
            name: "lead-activity",
            component: ActivityLog,
          },
          {
            path: ":id/lead-tasks",
            name: "lead-tasks",
            component: TasksPage,
          },
          {
            path: "/manage-task/view/:id?/:leadId?",
            name: "task-view",
            component: TaskView,
          },
          {
            path: ":id/lead-messages",
            name: "lead-messages",
            component: MessagesPage,
          },
          {
            path: ":id/lead-notes",
            name: "lead-notes",
            component: NotesPage,
          },
          {
            path: ":id/lead-searches",
            name: "lead-searches",
            component: LeadSearches,
          },
          {
            path: ":leadId/manage-task/:id?",
            name: "task",
            component: TaskPage,
          },
        ],
      },
    ],
  },
  {
    path: "/add-lead",
    name: "addLead",
    component: AddLeadPage,
  },
  {
    path: "/leads-categories",
    name: "leads-categories",
    component: LeadCategoriesPage,
  },
  {
    path: "/add-lead-category",
    name: "add-lead-category",
    component: AddLeadCategoryPage,
  },
  {
    path: "/send-message/:type/:edit?/:disable?/:reply?/:mass?/:leadId?/:listId?/:share?",
    name: "send-message",
    component: () =>
      import("@incomnetworking/app-agent-view-send-message-layout"),
  },
  {
    path: "/saved-filters",
    name: "saved-filters",
    component: () => import("@incomnetworking/app-agent-view-saved-filters"),
  },
];
const router = createRouter({
  history: createWebHistory("/"),
  routes,
});

export default router;
