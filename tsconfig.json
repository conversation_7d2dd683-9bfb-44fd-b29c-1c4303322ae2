{
  "compilerOptions": {
    "declaration": true,                    // ← this is key
    "emitDeclarationOnly": true,           // ← generate .d.ts only (no .js)
    "outDir": "dist",                      // ← output folder
    "rootDir": "src",                      // ← your source folder
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "preserveSymlinks": true,
    "jsx": "preserve",
    "importHelpers": true,
    "sourceMap": true,
    "baseUrl": ".",
  },
  "include": ["src", "src/global.d.ts", "src/shums-vue.d.ts"],
  "exclude": ["node_modules", "dist"]
}
