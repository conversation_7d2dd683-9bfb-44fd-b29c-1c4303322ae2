<template>
  <ion-page>
    <ion-split-pane when="md" content-id="main">
      <ion-menu content-id="main">
        <ion-content class="ion-padding">
          <LeadsPage />
        </ion-content>
      </ion-menu>
      <!-- Main content area -->
      <div class="ion-page" id="main">
        <ion-router-outlet />
      </div>
    </ion-split-pane>
  </ion-page>
</template>
<script lang="ts">
import { defineComponent, ref, ComponentPublicInstance } from 'vue';
import {
  actionSheetController,
  IonButton,
  IonContent,
  IonFooter,
  IonHeader,
  IonIcon,
  IonList,
  IonPage,
  IonSpinner,
  IonToolbar,
  menuController,
  IonLabel,
  IonMenu,
  IonSkeletonText,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  InfiniteScrollCustomEvent,
  IonItem,
  IonSplitPane,
  IonRefresher,
  IonRefresherContent,
  IonRouterOutlet,
  RefresherCustomEvent,
} from '@ionic/vue';
import { Tabs } from '@incomnetworking/agent-app-tabs-controler-store';
import LeadsPage from "@incomnetworking/app-agent-view-leads-page";
enum LeadsPopoverMenuActions {
  SelectLeads = 'SelectLeads',
  EditCategories = 'EditCategories',
  MassMessages = 'MassMessages',
}

export default defineComponent({
  components: {
    actionSheetController,
    IonRouterOutlet,
    IonMenu,
    IonButton,
    IonContent,
    IonFooter,
    IonHeader,
    IonIcon,
    IonList,
    IonPage,
    IonSpinner,
    IonToolbar,
    menuController,
    IonLabel,
    IonSkeletonText,
    IonInfiniteScroll,
    IonInfiniteScrollContent,
    IonItem,
    IonSplitPane,
    IonRefresher,
    IonRefresherContent,
    LeadsPage,
  },
  ionViewWillEnter() {
  },
  ionViewDidEnter() {
    
  },
  data() {
    return {
      isSplit: false,
    };
  },
  computed: {
    
  },
  methods: {
  },
  mounted: function(){
    
  },
  beforeUnmount: function(){
    
  }
});
</script>
