<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Welcome landing</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">select any lead</ion-content>
  </ion-page>
</template>
<script lang="ts">
import { defineComponent, ref, ComponentPublicInstance } from "vue";
import {
  actionSheetController,
  IonButton,
  IonContent,
  IonFooter,
  IonHeader,
  IonTitle,
  IonIcon,
  IonList,
  IonPage,
  IonSpinner,
  IonToolbar,
  IonButtons,
  menuController,
  IonLabel,
  IonMenu,
  IonSkeletonText,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  InfiniteScrollCustomEvent,
  IonItem,
  IonSplitPane,
  IonRefresher,
  IonRefresherContent,
  IonRouterOutlet,
  RefresherCustomEvent,
} from "@ionic/vue";
export default defineComponent({
  components: {
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
  },
  data() {
    return {};
  },
  computed: {},
  methods: {},
});
</script>
