<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Welcome landing - TEST</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <h1>App is working!</h1>
      <p>Select any lead from the menu</p>
      <p>Debug info: {{ new Date().toISOString() }}</p>
    </ion-content>
  </ion-page>
</template>
<script lang="ts">
import { defineComponent, ref, ComponentPublicInstance } from "vue";
import {
  actionSheetController,
  IonButton,
  IonContent,
  IonFooter,
  IonHeader,
  IonTitle,
  IonIcon,
  IonList,
  IonPage,
  IonSpinner,
  IonToolbar,
  IonButtons,
  menuController,
  IonLabel,
  IonMenu,
  IonSkeletonText,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  InfiniteScrollCustomEvent,
  IonItem,
  IonSplitPane,
  IonRefresher,
  IonRefresherContent,
  IonRouterOutlet,
  RefresherCustomEvent,
} from "@ionic/vue";
export default defineComponent({
  components: {
    IonPage,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
  },
  data() {
    return {};
  },
  computed: {},
  methods: {},
});
</script>
