{"name": "@incomnetworking/app-agent-double-view-leads-page", "productName": "app-agent-double-view-leads-page", "description": "Vue component view for DoubeLeadsPage", "version": "1.0.0", "license": "SEE LICENSE IN <LICENSE.txt>", "files": ["dist", "src"], "main": "src/index.js", "module": "src/index.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"import": "./src/index.js", "require": "./dist/app-agent-double-view-leads-page.umd.cjs"}}, "scripts": {"dev": "vite dev", "build": "vite build", "build:types": "tsc", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix "}, "devDependencies": {"@capacitor/device": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@gormartsen/vue-dataset": "^1.0.1", "@gormartsen/vue-debug": "^1.0.0", "@incomnetworking/app-agent-fonts-scss": "^1.0.0", "@incomnetworking/app-agent-global-scss": "^1.0.0", "@incomnetworking/app-agent-interface-styles": "^1.0.0", "@incomnetworking/app-agent-service-activity": "^1.0.2", "@incomnetworking/app-agent-service-leads": "^1.0.0", "@incomnetworking/app-agent-service-messages": "^1.0.1", "@incomnetworking/app-agent-service-mobileappsettings": "^1.0.2", "@incomnetworking/app-agent-service-notifications": "^1.0.0", "@incomnetworking/app-agent-view-add-lead-category-page": "^1.0.0", "@incomnetworking/app-agent-view-add-lead-page": "^1.0.1", "@incomnetworking/app-agent-view-lead-categories-page": "^1.0.0", "@incomnetworking/app-agent-view-login-page": "^1.0.2", "@incomnetworking/app-agent-view-send-message-layout": "^1.0.0", "@incomnetworking/interface-text": "^1.0.23", "@incomnetworking/interface-translations": "^1.0.0", "@incomnetworking/page-builder-library": "^1.0.2", "@incomnetworking/pagebuild-api-settings-dashboard": "^1.0.0", "@incomnetworking/pagebuild-cdn-settings": "^1.0.1", "@incomnetworking/pagebuilder-ckeditor": "^1.0.2", "@incomnetworking/vue-plugin-app-helper": "^1.0.22", "@incomnetworking/vue-plugin-auth": "^1.0.3", "@ionic/vue": "^8.5.8", "@ionic/vue-router": "^8.5.8", "@microservice-framework/microservice-client": "github:microservice-framework/microservice-client#2.x", "@microservice-framework/vue-api-client": "github:microservice-framework/vue-api-client#2.x", "@rushstack/eslint-patch": "^1.1.0", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "capacitor-plugin-silent-notifications": "^6.0.1", "dayjs": "^1.11.13", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "moment": "^2.30.1", "pinia-plugin-persistedstate": "^4.3.0", "prettier": "^2.5.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-external-globals": "^0.6.1", "sass": "^1.53.0", "typescript": "4.9.5", "vite": "^3.0.2", "vite-plugin-inject-externals": "^1.1.1", "vue": "^3.3.10", "vue-persist-state": "^2.0.0", "vue-router": "4.4.0"}, "dependencies": {"@capacitor/core": "^7.2.0", "@capacitor/preferences": "^7.0.1", "@incomnetworking/agent-app-helper-functions": "^1.0.10", "@incomnetworking/agent-app-tabs-controler-store": "^1.0.2", "@incomnetworking/app-agent-auth-store": "^1.0.0", "@incomnetworking/app-agent-global-preferences-store": "^1.0.1", "@incomnetworking/app-agent-lead-profile-header-vue-components": "^1.0.2", "@incomnetworking/app-agent-lead-profile-menu-with-actions-vue-component": "^1.0.2", "@incomnetworking/app-agent-leads-list-vue-component": "^1.0.1", "@incomnetworking/app-agent-main-menu-vue-component": "^1.0.0", "@incomnetworking/app-agent-mass-message-action-sheet-vue-component": "^1.0.0", "@incomnetworking/app-agent-mass-message-modal-vue-component": "^1.0.0", "@incomnetworking/app-agent-message-store": "^1.0.0", "@incomnetworking/app-agent-multi-select-typehead-vue-component": "^1.0.1", "@incomnetworking/app-agent-profile-store": "^1.0.0", "@incomnetworking/app-agent-push-notification-store": "^1.0.0", "@incomnetworking/app-agent-user-config-store": "^1.0.2", "@incomnetworking/app-agent-view-activity-log-page": "^1.0.0", "@incomnetworking/app-agent-view-leads-page": "^1.0.0", "@incomnetworking/app-agent-view-manage-lead-page": "^1.0.2", "@incomnetworking/app-agent-view-manage-task-page": "^1.0.1", "@incomnetworking/app-agent-view-manage-task-view": "^1.0.1", "@incomnetworking/app-agent-view-messages-page": "^1.0.1", "@incomnetworking/app-agent-view-notes-page": "^1.0.1", "@incomnetworking/app-agent-view-saved-filters": "^1.0.4", "@incomnetworking/app-agent-view-saved-searches-page": "^1.0.0", "@incomnetworking/app-agent-view-searches-page": "^1.0.1", "@incomnetworking/app-agent-view-tabs-page": "^1.0.1", "@incomnetworking/app-agent-view-tasks-page": "^1.0.1", "@incomnetworking/vue-component-app-action-sheet": "^1.0.1", "@incomnetworking/vue-component-app-terms-and-conditions": "^1.0.0", "@incomnetworking/vue-components-app-common": "^1.0.0", "@incomnetworking/vue-components-app-icons": "^1.0.2", "lodash-es": "^4.17.21", "pinia": "^3.0.2"}}